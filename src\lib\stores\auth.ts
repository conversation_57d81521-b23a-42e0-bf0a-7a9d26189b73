// Authentication store

import { writable, derived } from 'svelte/store';
import type { TelegramUser } from '../types';
import { TelegramAuth } from '../telegram/auth';

interface AuthState {
	isAuthenticated: boolean;
	isLoading: boolean;
	user: TelegramUser | null;
	sessionToken: string | null;
	error: string | null;
}

const initialState: AuthState = {
	isAuthenticated: false,
	isLoading: true,
	user: null,
	sessionToken: null,
	error: null
};

export const authStore = writable<AuthState>(initialState);

// Derived stores
export const isAuthenticated = derived(authStore, ($auth) => {
	console.log('AuthStore - 认证状态:', $auth.isAuthenticated);
	return $auth.isAuthenticated;
});

export const currentUser = derived(authStore, ($auth) => {
	console.log('AuthStore - 当前用户:', $auth.user);
	return $auth.user;
});

export const authLoading = derived(authStore, ($auth) => $auth.isLoading);
export const authError = derived(authStore, ($auth) => $auth.error);

// Actions
export const authActions = {
	/**
	 * 初始化认证状态 - 检查现有会话
	 */
	async init() {
		authStore.update((state) => ({ ...state, isLoading: true, error: null }));

		try {
			// 检查本地存储的会话
			const storedToken = TelegramAuth.getStoredSessionToken();
			const storedUser = TelegramAuth.getStoredUser();

			if (storedToken && storedUser) {
				console.log('🔍 检查存储的会话...');
				// 验证存储的会话
				const validation = await TelegramAuth.validateSession(storedToken);

				if (validation.success && validation.user) {
					console.log('✅ 会话验证成功');
					authStore.update((state) => ({
						...state,
						isAuthenticated: true,
						isLoading: false,
						user: validation.user,
						sessionToken: storedToken,
						error: null
					}));
					return { success: true, hasValidSession: true };
				} else {
					console.log('❌ 会话验证失败，清除存储');
					// 清除无效的会话
					TelegramAuth.clearStoredAuth();
				}
			}

			console.log('ℹ️ 没有有效的存储会话，需要重新认证');
			authStore.update((state) => ({
				...state,
				isAuthenticated: false,
				isLoading: false,
				error: null
			}));

			return { success: true, hasValidSession: false };
		} catch (error) {
			console.error('❌ 认证初始化失败:', error);
			authStore.update((state) => ({
				...state,
				isAuthenticated: false,
				isLoading: false,
				error: error instanceof Error ? error.message : '初始化失败'
			}));
			return { success: false, error: error instanceof Error ? error.message : '初始化失败' };
		}
	},

	/**
	 * 完整的Telegram认证流程
	 */
	async initWithTelegram() {
		authStore.update((state) => ({ ...state, isLoading: true, error: null }));

		try {
			// 1. 先检查现有会话
			const initResult = await this.init();
			if (initResult.hasValidSession) {
				return { success: true, message: '使用现有会话' };
			}

			// 2. 获取Telegram数据
			console.log('🔍 获取Telegram数据...');
			const { getTelegramDataFromAnySource, validateTelegramData } = await import(
				'$lib/utils/telegram-data-parser'
			);
			const telegramData = getTelegramDataFromAnySource();

			if (!telegramData || !validateTelegramData(telegramData)) {
				throw new Error('无法获取有效的Telegram用户数据');
			}

			console.log('✅ 获取到Telegram用户数据:', telegramData.user);

			// 3. 使用Telegram数据登录
			const loginResult = await this.loginWithTelegram(telegramData.user, telegramData.start_param);

			if (loginResult.success) {
				console.log('✅ Telegram认证成功');
				return { success: true, message: '认证成功' };
			} else {
				throw new Error(loginResult.error || '登录失败');
			}
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : '认证失败';
			console.error('❌ Telegram认证失败:', errorMessage);

			authStore.update((state) => ({
				...state,
				isAuthenticated: false,
				isLoading: false,
				error: errorMessage
			}));

			return { success: false, error: errorMessage };
		}
	},

	/**
	 * 使用 Telegram 数据登录
	 */
	async loginWithTelegram(user: TelegramUser, referrerCode?: string) {
		authStore.update((state) => ({ ...state, isLoading: true, error: null }));

		try {
			console.log('🔄 开始登录流程...');
			const session = await TelegramAuth.createSession(user, referrerCode);

			if (session.success && session.sessionToken && session.user) {
				console.log('✅ 会话创建成功，更新认证状态');

				// 存储会话信息
				TelegramAuth.storeSessionToken(session.sessionToken);
				TelegramAuth.storeUser(session.user);

				authStore.update((state) => ({
					...state,
					isAuthenticated: true,
					isLoading: false,
					user: session.user,
					sessionToken: session.sessionToken!,
					error: null
				}));

				console.log('✅ 认证状态更新完成');

				// 认证成功后，尝试加载用户profile
				try {
					console.log('🔄 认证成功，开始加载用户profile...');
					const { userActions } = await import('./user');
					await userActions.loadProfileByTelegramId(user.id);
					console.log('✅ 用户profile加载完成');
				} catch (profileError) {
					console.log('ℹ️ 用户profile加载失败（可能是新用户）:', profileError);
					// 不影响认证成功的结果，profile加载失败可能是新用户
				}

				return { success: true };
			} else {
				throw new Error(session.error || '登录失败');
			}
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : '登录失败';
			console.error('❌ 登录失败:', errorMessage);

			authStore.update((state) => ({
				...state,
				isAuthenticated: false,
				isLoading: false,
				error: errorMessage
			}));

			return { success: false, error: errorMessage };
		}
	},

	/**
	 * 登出
	 */
	async logout() {
		authStore.update((state) => ({ ...state, isLoading: true }));

		try {
			await TelegramAuth.logout();
			TelegramAuth.clearStoredAuth();

			authStore.set(initialState);
		} catch (error) {
			// 即使登出失败，也清除本地状态
			TelegramAuth.clearStoredAuth();
			authStore.set(initialState);
		}
	},

	/**
	 * 更新用户信息
	 */
	updateUser(user: Partial<TelegramUser>) {
		authStore.update((state) => {
			if (state.user) {
				const updatedUser = { ...state.user, ...user };
				TelegramAuth.storeUser(updatedUser);
				return { ...state, user: updatedUser };
			}
			return state;
		});
	},

	/**
	 * 清除错误
	 */
	clearError() {
		authStore.update((state) => ({ ...state, error: null }));
	}
};
