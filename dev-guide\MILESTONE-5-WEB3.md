# 里程碑 5 指南：Web3 集成

**目标:** 将 TON (The Open Network) 生态集成到您的应用中。允许用户连接他们的 TON 钱包，为未来的代币经济系统和 Web3 功能奠定基础。

**前置条件:** 已完成 [里程碑 4: 成长与商业化系统](./MILESTONE-4-GROWTH.md) 中的所有任务。

---

### 任务 5.1: 集成 TON 钱包连接功能

**目标:** 让用户能够安全地将他们的 TON 钱包（如 Tonkeeper）连接到他们的 TMA 应用账户，并将钱包地址与用户身份进行绑定。

#### **步骤 1: 安装并配置 TON Connect UI**
TON Connect UI 是官方推荐的、用于处理钱包连接流程的前端库。它会自动处理好弹出窗口、二维码、以及与不同钱包 App 的通信。

1.  **安装依赖:**
    ```bash
    pnpm install @tonconnect/ui
    ```
2.  **创建 Manifest 文件:**
    * 在您的 `static` 文件夹下 (例如 `static/tonconnect-manifest.json`)，创建一个清单文件。这个文件会告诉钱包您的应用是什么。
    ```json
    {
      "url": "[https://your-app-url.com](https://your-app-url.com)", // 您应用最终的线上地址
      "name": "BlueX TMA",
      "iconUrl": "[https://your-app-url.com/logo.png](https://your-app-url.com/logo.png)"
    }
    ```
3.  **在根布局中初始化 TON Connect:** 为了让连接器实例在整个应用中保持单例和响应式，我们可以在根布局 `src/routes/+layout.svelte` 中初始化它。

    ```svelte
    <script lang="ts">
        import { onMount } from 'svelte';
        import { TonConnectUI } from '@tonconnect/ui';
        import { setTonConnectUI } from '$lib/stores/tonStore'; // 我们将创建一个 store 来保存实例

        onMount(() => {
            const tonConnectUI = new TonConnectUI({
                manifestUrl: '[https://your-app-url.com/tonconnect-manifest.json](https://your-app-url.com/tonconnect-manifest.json)', // 替换为您的线上地址
                buttonRootId: 'ton-connect-button-root' // 指定一个DOM元素来挂载官方按钮
            });

            setTonConnectUI(tonConnectUI); // 将实例存入 Svelte store 以便全局访问
        });
    </script>
    
    ```
    *您需要创建一个简单的 writable store `src/lib/stores/tonStore.ts` 来存放这个 `tonConnectUI` 实例。*

#### **步骤 2: 创建钱包连接与关联的后端 API**
当用户连接钱包后，我们需要在后端验证并保存这个关联。

1.  **安装后端验证库:**
    ```bash
    pnpm install @tonconnect/sdk
    ```
2.  **创建 API 端点 `src/routes/api/wallet/link/+server.ts`:**
    ```typescript
    // src/routes/api/wallet/link/+server.ts
    import { json, error as skError } from '@sveltejs/kit';
    import { TonConnect, isTelegramUrl } from '@tonconnect/sdk';
    import { db } from '$lib/server/db';
    import { users, keys } from '$lib/server/db/schema';
    import { eq } from 'drizzle-orm';
    import type { RequestHandler } from './$types';

    export const POST: RequestHandler = async ({ request, locals }) => {
        const user = locals.user;
        if (!user) throw skError(401, 'Unauthorized');

        const { proof, client_id, address } = await request.json();

        // 后端验证逻辑... 这是一个简化的示例
        // 实际场景需要更复杂的验证来确保 proof 的时效性和唯一性

        // 验证成功后，更新数据库
        try {
            // 1. 在 users 表中记录钱包地址
            await db.update(users)
                .set({ tonWalletAddress: address })
                .where(eq(users.id, user.id));
            
            // 2. 在 keys 表中为该用户新增一个 'ton' 类型的凭证
            await db.insert(keys).values({
                id: `ton:${address}`,
                userId: user.id,
                hashedPassword: null // 非密码登录，此字段为空
            }).onConflictDoNothing(); // 避免重复关联时报错

        } catch (dbError) {
            // 处理数据库错误，比如钱包地址已被其他用户关联
            throw skError(500, '关联钱包失败');
        }

        return json({ success: true });
    };
    ```

#### **步骤 3: 构建前端连接按钮组件**
创建一个可复用的组件来处理连接逻辑。

```svelte
<script lang="ts">
    import { tonConnectUIStore } from '$lib/stores/tonStore';
    import { onMount } from 'svelte';

    let unsubscribe;

    onMount(() => {
        unsubscribe = tonConnectUIStore.subscribe(async (sdk) => {
            if (!sdk?.connected) return;

            // 当连接状态变化时，获取凭证并发送到后端
            const proofPayload = {
                address: sdk.account.address,
                proof: sdk.account.wallet.connectItems.tonProof,
                client_id: 'YOUR_CLIENT_ID_FROM_TON_CONNECT' // 需要从连接数据中获取
            };
            
            await fetch('/api/wallet/link', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(proofPayload)
            });
        });

        return () => unsubscribe?.();
    });
</script>

<div id="ton-connect-button-root"></div>
任务 5.2: 设计与实现初步的代币经济系统
目标: 建立一个基础的、应用内的经济循环框架，为未来发行和使用代币做准备。

重要声明: 真正的链上代币发行和交易是一个极其复杂的领域，涉及智能合约、高安全性、法律合规等。本阶段我们的目标是先建立一个“链下账本”，并在必要时与链上交互。

步骤 1: 概念设计
明确积分和代币的用途。

积分 (Points): 可通过应用内行为（签到、完成任务）免费获得，用于兑换一些基础的消耗品或特权。
代币 (Tokens): 代表真实价值，可通过“积分兑换”、“活动奖励”或未来可能的“充值”获得。可用于解锁高级/永久性功能、打赏、或参与社区治理。
步骤 2: 数据库扩展
我们可能需要一张新表来记录应用内的代币流水。

SQL

-- token_transactions 表
CREATE TABLE token_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    amount NUMERIC(20, 9) NOT NULL, -- 使用 NUMERIC 来精确存储代币数量
    type TEXT NOT NULL, -- 'redeem_from_points', 'tip_sent', 'feature_unlocked', 'deposit', 'withdrawal'
    description TEXT,
    related_tx_hash TEXT, -- 关联的链上交易哈希 (如果是链上操作)
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
同时，在 users 表中增加一个 token_balance NUMERIC(20, 9) NOT NULL DEFAULT 0 字段。

步骤 3: 创建后端代币服务
与 points.service.ts 类似，创建一个 src/lib/server/services/token.service.ts。

grantTokens(userId, amount, ...): 增加用户在我们数据库中的代币余额，并记录流水。
spendTokens(userId, amount, ...): 减少用户在我们数据库中的代币余额。
步骤 4: 实现一个核心功能 - “积分兑换代币”
这是连接两个经济系统的桥梁。

前端: 在 /growth 页面，增加一个“积分兑换”区域，包含输入框和按钮。
后端: 创建一个新的 action，例如 redeemTokens。
Action 逻辑:
启动一个数据库事务 db.transaction(...)。
检查用户积分是否足够。
调用 points.service.ts 的 addPoints 方法扣除积分 (传入负值)。
调用 token.service.ts 的 grantTokens 方法增加代币。
事务成功提交。
返回成功信息给前端。
关于链上交互 (高级/未来)
充值 (Deposit): 后端需要为每个用户生成一个独立的TON收款地址，并通过TON的API或节点来监控这些地址的入账交易。一旦检测到入账，就调用 grantTokens 更新用户的链下余额。
提现 (Withdrawal): 这需要您的后端服务器管理一个“热钱包”（一个拥有私钥的钱包）。当用户请求提现时，您的后端需要构造、签名并广播一笔TON链上交易。这是一个极高风险的操作，需要顶级的安全措施来保护您的热钱包私钥。
建议: 在项目初期，专注于完善链下账本（积分和代币的数据库记录和逻辑），这已经能支撑起非常丰富的应用内经济玩法。链上交互可以作为更长远的目标。

AI Copilot 指示: 里程碑 5 的任务已规划完毕。这是项目的创新和差异化所在。请从任务 5.1 开始，为应用集成 TON 钱包连接功能。完成后，请记得更新 PROGRESS_LOG.md。