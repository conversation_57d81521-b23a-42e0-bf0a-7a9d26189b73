import { fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { advancedProfileSchema } from '$lib/schemas/profile';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import { encodeRoles } from '$lib/utils/kinkUtils';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	// 检查用户是否已登录
	if (!locals.user) {
		throw redirect(303, '/');
	}

	// 加载用户现有的高级资料数据
	const existingUser = await db.query.users.findFirst({
		where: eq(users.id, locals.user.id),
		columns: {
			heightCm: true,
			weightKg: true,
			relationshipStatus: true,
			kinkCategoryBitmask: true,
			kinkRatings: true,
			blockVisibilityFromBodyTypes: true,
			blockVisibilityFromPresentationStyles: true,
			blockVisibilityFromOrientations: true
		}
	});

	// 预填充表单数据
	const initialData = existingUser
		? {
				heightCm: existingUser.heightCm,
				weightKg: existingUser.weightKg,
				relationshipStatus: existingUser.relationshipStatus,
				kinkRatings: existingUser.kinkRatings || {},
				blockVisibilityFromBodyTypes: existingUser.blockVisibilityFromBodyTypes || [],
				blockVisibilityFromPresentationStyles:
					existingUser.blockVisibilityFromPresentationStyles || [],
				blockVisibilityFromOrientations: existingUser.blockVisibilityFromOrientations || []
			}
		: {};

	return {
		form: await superValidate(initialData, zod(advancedProfileSchema))
	};
};

export const actions: Actions = {
	default: async ({ request, locals }) => {
		if (!locals.user) {
			throw fail(401, { message: '未登录，请重新登录' });
		}

		const form = await superValidate(request, zod(advancedProfileSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			console.log('💾 保存高级资料，用户ID:', locals.user.id);
			console.log('📝 表单数据:', form.data);

			// 处理kink角色编码
			const kinkBitmask = form.data.kinkRoles ? encodeRoles(form.data.kinkRoles) : 0;

			// 更新用户高级资料
			const updateData = {
				heightCm: form.data.heightCm,
				weightKg: form.data.weightKg,
				relationshipStatus: form.data.relationshipStatus,
				kinkCategoryBitmask: kinkBitmask,
				kinkRatings: form.data.kinkRatings || {},
				blockVisibilityFromBodyTypes: form.data.blockVisibilityFromBodyTypes || [],
				blockVisibilityFromPresentationStyles:
					form.data.blockVisibilityFromPresentationStyles || [],
				blockVisibilityFromOrientations: form.data.blockVisibilityFromOrientations || [],
				updatedAt: new Date()
			};

			console.log('🔄 更新数据库，用户ID:', locals.user.id);
			console.log('📊 更新数据:', updateData);

			const updatedUsers = await db
				.update(users)
				.set(updateData)
				.where(eq(users.id, locals.user.id))
				.returning();

			if (updatedUsers.length === 0) {
				throw new Error('更新用户高级资料失败');
			}

			console.log('✅ 高级资料保存成功:', updatedUsers[0]);

			// 重定向到profile页面查看结果
			throw redirect(302, '/profile');
		} catch (error) {
			console.error('❌ 保存高级资料失败:', error);
			return fail(500, {
				form,
				message: error instanceof Error ? error.message : '保存失败，请重试'
			});
		}
	}
};
