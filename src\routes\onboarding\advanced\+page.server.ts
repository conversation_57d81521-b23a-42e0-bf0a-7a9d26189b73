import { fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { kinkPreferencesSchema, privacySettingsSchema } from '$lib/schemas/user';
import { z } from 'zod';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';

// 合并 schema
const advancedSchema = kinkPreferencesSchema.merge(privacySettingsSchema);

export const load: PageServerLoad = async () => {
	return {
		form: await superValidate(zod(advancedSchema))
	};
};

export const actions: Actions = {
	default: async (event) => {
		const form = await superValidate(event, zod(advancedSchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		try {
			// 从cookie中获取session token
			const sessionToken = event.cookies.get('session');
			if (!sessionToken) {
				return fail(401, {
					form,
					message: '未登录，请重新登录'
				});
			}

			// 从session token中提取telegram user id
			const tokenParts = sessionToken.split('_');
			if (tokenParts.length < 3 || tokenParts[0] !== 'session') {
				return fail(401, {
					form,
					message: '无效的会话，请重新登录'
				});
			}

			const telegramUserId = parseInt(tokenParts[1]);
			if (isNaN(telegramUserId)) {
				return fail(401, {
					form,
					message: '无效的会话，请重新登录'
				});
			}

			console.log('💾 保存高级偏好，Telegram用户ID:', telegramUserId);
			console.log('📝 表单数据:', form.data);

			// 查找现有用户
			const existingUsers = await db
				.select()
				.from(users)
				.where(eq(users.telegramUserId, telegramUserId))
				.limit(1);

			if (existingUsers.length === 0) {
				return fail(404, {
					form,
					message: '用户不存在，请重新登录'
				});
			}

			const user = existingUsers[0];

			// 更新用户高级偏好
			const updateData = {
				// 从表单数据中提取kink偏好和隐私设置
				kinkRatings: form.data.kinkRatings || {},
				blockVisibilityFromBodyTypes: form.data.blockVisibilityFromBodyTypes || [],
				blockVisibilityFromOrientations: form.data.blockVisibilityFromOrientations || [],
				blockVisibilityFromAgeRanges: form.data.blockVisibilityFromAgeRanges || [],
				updatedAt: new Date()
			};

			console.log('🔄 更新数据库，用户ID:', user.id);
			console.log('📊 更新数据:', updateData);

			const updatedUsers = await db
				.update(users)
				.set(updateData)
				.where(eq(users.id, user.id))
				.returning();

			if (updatedUsers.length === 0) {
				throw new Error('更新用户高级偏好失败');
			}

			console.log('✅ 高级偏好保存成功:', updatedUsers[0]);

			// 重定向到发现页面
			throw redirect(302, '/discover');
		} catch (error) {
			console.error('❌ 保存高级偏好失败:', error);
			return fail(500, {
				form,
				message: error instanceof Error ? error.message : '保存失败，请重试'
			});
		}
	}
};
