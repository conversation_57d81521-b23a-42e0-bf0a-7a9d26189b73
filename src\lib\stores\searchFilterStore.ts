// src/lib/stores/searchFiltersStore.ts
import { writable } from 'svelte/store';
import type { bodyTypeEnum, orientationEnum, presentationStyleEnum } from '$lib/server/db/schema'; // 直接从Drizzle Schema导入类型

// 筛选条件直接对应用户的字段，高级字段为可选
export interface SearchFilters {
    age_min?: number;
    age_max?: number;
    country?: string;
    city?: string;
    body_type?: (typeof bodyTypeEnum.enumValues)[number];

    // 高级筛选条件
    orientation?: (typeof orientationEnum.enumValues)[number];
    presentation_style?: (typeof presentationStyleEnum.enumValues)[number];
    // ...其他高级筛选
}

export type SearchMode = 'basic' | 'advanced' | 'vip';

interface SearchOptionsState {
    mode: SearchMode;
    filters: SearchFilters;
    sortBy: 'lastActive' | 'trustScore' | 'relevance';
}

const initialState: SearchOptionsState = {
	mode: 'basic',
	filters: {
        age_min: 18,
        age_max: 55,
    },
	sortBy: 'lastActive'
};

function createSearchOptionsStore() {
    const { subscribe, set, update } = writable<SearchOptionsState>(initialState);

    return {
        subscribe,
        updateFilters: (newFilters: Partial<SearchFilters>) => {
            update(state => ({
                ...state,
                filters: { ...state.filters, ...newFilters },
            }));
        },
        setSortBy: (sortBy: SearchOptionsState['sortBy']) => {
            update(state => ({ ...state, sortBy }));
        },
        reset: () => {
            set(initialState);
        }
    };
}

export const searchOptionsStore = createSearchOptionsStore();