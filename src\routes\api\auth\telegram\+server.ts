import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import { generateKinkMapCode } from '$lib/utils/helpers';
import { nanoid } from 'nanoid';
import { validateTelegramData } from '$lib/server/telegram-validator';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const body = await request.json();
		console.log('🔍 收到认证请求:', {
			hasTelegramUser: !!body.telegramUser,
			hasReferrerCode: !!body.referrerCode,
			hasInitData: !!body.initData
		});

		// 方式1: 直接传递用户数据（兼容现有代码）
		if (body.telegramUser) {
			const { telegramUser, referrerCode } = body;

			if (!telegramUser || !telegramUser.id) {
				return json({ error: 'Invalid Telegram user data' }, { status: 400 });
			}

			console.log('✅ 使用直接用户数据认证:', telegramUser.first_name, telegramUser.id);
			return await processUserAuth(telegramUser, referrerCode, cookies);
		}

		// 方式2: 验证原始 Telegram 初始化数据
		if (body.initData) {
			const validation = validateTelegramData(body.initData);

			if (!validation.isValid || !validation.data?.user) {
				console.error('❌ Telegram数据验证失败:', validation.error);
				return json({ error: validation.error || 'Invalid Telegram data' }, { status: 400 });
			}

			console.log(
				'✅ Telegram数据验证成功:',
				validation.data.user.first_name,
				validation.data.user.id
			);
			return await processUserAuth(validation.data.user, validation.data.start_param, cookies);
		}

		return json({ error: 'Missing Telegram user data or initData' }, { status: 400 });
	} catch (error) {
		console.error('❌ Telegram auth error:', error);
		return json({ error: 'Authentication failed' }, { status: 500 });
	}
};

/**
 * 处理用户认证的核心逻辑
 */
async function processUserAuth(telegramUser: any, referrerCode: string | undefined, cookies: any) {
	try {
		console.log('🔍 处理用户认证:', telegramUser.first_name, telegramUser.id);
		console.log('📊 API: 查询现有用户...');

		// 检查用户是否已存在
		const existingUsers = await db
			.select()
			.from(users)
			.where(eq(users.telegramUserId, telegramUser.id))
			.limit(1);

		console.log('📊 API: 数据库查询结果:', existingUsers.length > 0 ? '用户已存在' : '新用户');

		let user = existingUsers[0];

		if (!user) {
			console.log('👤 创建新用户...');
			console.log('📊 API: 新用户数据准备');

			// 创建新用户
			const newUserData = {
				telegramUserId: telegramUser.id,
				telegramUsername: telegramUser.username || null,
				nickname: telegramUser.first_name,
				kinkMapCode: generateKinkMapCode(),
				profileImageUrl: telegramUser.photo_url || null,
				hasAvatar: !!telegramUser.photo_url,
				profileCompletenessScore: 20, // 基础分数
				vipLevel: telegramUser.is_premium ? 1 : 0,
				pointBalance: 100, // 注册奖励
				trustScore: 50
			};

			console.log('📊 API: 新用户数据:', {
				telegramUserId: newUserData.telegramUserId,
				nickname: newUserData.nickname,
				kinkMapCode: newUserData.kinkMapCode
			});

			const insertedUsers = await db.insert(users).values(newUserData).returning();

			user = insertedUsers[0];
			console.log('✅ 新用户创建成功:', user.id);
		} else {
			console.log('👤 更新现有用户...');
			// 更新现有用户的最后活跃时间
			const updatedUsers = await db
				.update(users)
				.set({
					lastActiveAt: new Date(),
					updatedAt: new Date()
				})
				.where(eq(users.id, user.id))
				.returning();

			user = updatedUsers[0];
			console.log('✅ 用户信息更新成功:', user.id);
		}

		// 生成会话令牌
		const sessionToken = `session_${telegramUser.id}_${nanoid()}`;
		console.log('🔑 生成会话令牌:', sessionToken.substring(0, 20) + '...');

		// 设置 cookie
		cookies.set('session', sessionToken, {
			path: '/',
			maxAge: 60 * 60 * 24 * 7, // 7 days
			sameSite: 'strict',
			httpOnly: true,
			secure: process.env.NODE_ENV === 'production'
		});

		const responseData = {
			sessionToken,
			user: {
				id: user.id,
				nickname: user.nickname,
				profileCompletenessScore: user.profileCompletenessScore,
				pointBalance: user.pointBalance,
				vipLevel: user.vipLevel,
				isVerified: user.isVerified,
				trustScore: user.trustScore,
				kinkMapCode: user.kinkMapCode,
				hasAvatar: user.hasAvatar,
				age: user.age,
				orientation: user.orientation,
				bodyType: user.bodyType,
				presentationStyle: user.presentationStyle,
				bio: user.bio,
				country: user.country,
				city: user.city,
				profileImageUrl: user.profileImageUrl,
				telegramUserId: user.telegramUserId,
				telegramUsername: user.telegramUsername
			}
		};

		console.log('✅ 认证成功，返回用户数据');
		return json(responseData);
	} catch (error) {
		console.error('❌ 用户认证处理失败:', error);
		throw error;
	}
}
