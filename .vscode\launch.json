{
	"version": "0.2.0",
	"configurations": [
		{
			"name": "SvelteKit: Server-Side",
			"type": "node",
			"request": "launch",
			"runtimeExecutable": "pnpm",
			"runtimeArgs": ["run", "dev"],
			"outputCapture": "std",
			"timeout": 90000,
			"sourceMaps": true,
			"autoAttachChildProcesses": true,
			"console": "integratedTerminal"
		},
		{
			"name": "SvelteKit: Client-Side",
			"type": "msedge", // 如果您主要使用 Edge，也可以改为 "msedge"
			"request": "launch",
			"url": "http://localhost:5173",
			"webRoot": "${workspaceFolder}"
		}
	],
	"compounds": [
		{
			"name": "SvelteKit: Full-Stack",
			"configurations": ["SvelteKit: Server-Side", "SvelteKit: Client-Side"],
			"stopAll": true
		}
	]
}
