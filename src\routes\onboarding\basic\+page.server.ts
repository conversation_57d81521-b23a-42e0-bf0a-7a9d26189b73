import { fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { basicProfileSchema } from '$lib/schemas/user';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async () => {
	return {
		form: await superValidate(zod(basicProfileSchema))
	};
};

export const actions: Actions = {
	default: async (event) => {
		const form = await superValidate(event, zod(basicProfileSchema));

		if (!form.valid) {
			return fail(400, {
				form
			});
		}

		try {
			// 从cookie中获取session token
			const sessionToken = event.cookies.get('session');
			if (!sessionToken) {
				return fail(401, {
					form,
					message: '未登录，请重新登录'
				});
			}

			// 从session token中提取telegram user id
			const tokenParts = sessionToken.split('_');
			if (tokenParts.length < 3 || tokenParts[0] !== 'session') {
				return fail(401, {
					form,
					message: '无效的会话，请重新登录'
				});
			}

			const telegramUserId = parseInt(tokenParts[1]);
			if (isNaN(telegramUserId)) {
				return fail(401, {
					form,
					message: '无效的会话，请重新登录'
				});
			}

			console.log('💾 保存基础资料，Telegram用户ID:', telegramUserId);
			console.log('📝 表单数据:', form.data);

			// 查找现有用户
			const existingUsers = await db
				.select()
				.from(users)
				.where(eq(users.telegramUserId, telegramUserId))
				.limit(1);

			if (existingUsers.length === 0) {
				return fail(404, {
					form,
					message: '用户不存在，请重新登录'
				});
			}

			const user = existingUsers[0];

			// 更新用户基础资料
			const updateData = {
				nickname: form.data.nickname,
				age: form.data.age,
				orientation: form.data.orientation,
				bodyType: form.data.bodyType,
				presentationStyle: form.data.presentationStyle,
				bio: form.data.bio || null,
				country: form.data.country || null,
				city: form.data.city || null,
				updatedAt: new Date()
			};

			console.log('🔄 更新数据库，用户ID:', user.id);
			console.log('📊 更新数据:', updateData);

			const updatedUsers = await db
				.update(users)
				.set(updateData)
				.where(eq(users.id, user.id))
				.returning();

			if (updatedUsers.length === 0) {
				throw new Error('更新用户资料失败');
			}

			console.log('✅ 基础资料保存成功:', updatedUsers[0]);

			// 重定向到发现页面
			throw redirect(302, '/discover');
		} catch (error) {
			console.error('❌ 保存基础资料失败:', error);
			return fail(500, {
				form,
				message: error instanceof Error ? error.message : '保存失败，请重试'
			});
		}
	}
};
