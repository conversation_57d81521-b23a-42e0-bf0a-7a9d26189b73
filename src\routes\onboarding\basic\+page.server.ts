import { fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms';
import { zod } from 'sveltekit-superforms/adapters';
import { basicProfileSchema } from '$lib/schemas/profile';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	// 检查用户是否已登录
	if (!locals.user) {
		throw redirect(303, '/');
	}

	return {
		form: await superValidate(zod(basicProfileSchema))
	};
};

export const actions: Actions = {
	default: async ({ request, locals }) => {
		// 检查用户是否已登录
		if (!locals.user) {
			return fail(401, { message: '未登录，请重新登录' });
		}

		const form = await superValidate(request, zod(basicProfileSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		try {
			console.log('💾 保存基础资料，用户ID:', locals.user.id);
			console.log('📝 表单数据:', form.data);

			// 更新用户基础资料
			const updateData = {
				nickname: form.data.nickname,
				age: form.data.age,
				orientation: form.data.orientation,
				bodyType: form.data.bodyType,
				presentationStyle: form.data.presentationStyle,
				bio: form.data.bio || null,
				country: form.data.country || null,
				city: form.data.city || null,
				updatedAt: new Date()
			};

			console.log('🔄 更新数据库，用户ID:', locals.user.id);
			console.log('📊 更新数据:', updateData);

			const updatedUsers = await db
				.update(users)
				.set(updateData)
				.where(eq(users.id, locals.user.id))
				.returning();

			if (updatedUsers.length === 0) {
				throw new Error('更新用户资料失败');
			}

			console.log('✅ 基础资料保存成功:', updatedUsers[0]);

			// 重定向到发现页面
			throw redirect(302, '/discover');
		} catch (error) {
			console.error('❌ 保存基础资料失败:', error);
			return fail(500, {
				form,
				message: error instanceof Error ? error.message : '保存失败，请重试'
			});
		}
	}
};
