// 测试认证系统的简单脚本
// 模拟用户登录流程

const testTelegramAuth = async () => {
	const baseUrl = 'https://localhost:5173';
	
	// 模拟Telegram用户数据
	const mockTelegramUser = {
		id: 123456789,
		first_name: 'Test',
		last_name: 'User',
		username: 'testuser',
		photo_url: null,
		is_premium: false
	};

	try {
		console.log('🔍 测试Telegram认证API...');
		
		// 发送认证请求
		const response = await fetch(`${baseUrl}/api/auth/telegram`, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
			},
			body: JSON.stringify({
				telegramUser: mockTelegramUser
			})
		});

		if (!response.ok) {
			throw new Error(`HTTP error! status: ${response.status}`);
		}

		const result = await response.json();
		console.log('✅ 认证成功:', result);

		// 检查是否设置了session cookie
		const cookies = response.headers.get('set-cookie');
		console.log('🍪 Cookies:', cookies);

		return result;
	} catch (error) {
		console.error('❌ 认证测试失败:', error);
		return null;
	}
};

// 如果在Node.js环境中运行
if (typeof window === 'undefined') {
	// 需要安装 node-fetch: npm install node-fetch
	// testTelegramAuth();
	console.log('请在浏览器控制台中运行此脚本');
} else {
	// 在浏览器中运行
	window.testAuth = testTelegramAuth;
	console.log('认证测试函数已加载，请运行: testAuth()');
}
