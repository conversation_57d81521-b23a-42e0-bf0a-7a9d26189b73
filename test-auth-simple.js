// 简单的认证系统测试
// 测试auth.ts中的核心函数

import * as auth from './src/lib/server/auth.js';

console.log('🔍 测试认证系统核心函数...');

// 测试1: 生成session token
console.log('\n1. 测试生成session token:');
const token = auth.generateSessionToken();
console.log('生成的token:', token);
console.log('Token长度:', token.length);

// 测试2: 测试cookie名称
console.log('\n2. 测试session cookie名称:');
console.log('Cookie名称:', auth.sessionCookieName);

// 测试3: 测试空白cookie创建
console.log('\n3. 测试创建空白cookie:');
const blankCookie = auth.createBlankSessionCookie();
console.log('空白cookie:', blankCookie);

console.log('\n✅ 基础认证函数测试完成');
