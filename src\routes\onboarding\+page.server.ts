import { redirect } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	// 检查用户是否已登录
	if (!locals.user) {
		throw redirect(303, '/');
	}

	try {
		// 从数据库加载完整的用户数据
		const fullUser = await db.query.users.findFirst({
			where: eq(users.id, locals.user.id)
		});

		if (!fullUser) {
			throw redirect(303, '/');
		}

		// 计算用户资料完成状态
		const hasBasicProfile = !!(
			fullUser.nickname && 
			fullUser.age && 
			fullUser.orientation && 
			fullUser.bodyType && 
			fullUser.presentationStyle
		);

		const hasAdvancedProfile = !!(
			fullUser.kinkCategoryBitmask && 
			fullUser.kinkCategoryBitmask > 0
		);

		// 计算完整度分数
		let profileCompletenessScore = 20; // 基础分数
		if (fullUser.age) profileCompletenessScore += 10;
		if (fullUser.bio) profileCompletenessScore += 10;
		if (fullUser.orientation) profileCompletenessScore += 15;
		if (fullUser.bodyType) profileCompletenessScore += 15;
		if (fullUser.presentationStyle) profileCompletenessScore += 15;
		if (fullUser.country) profileCompletenessScore += 5;
		if (fullUser.city) profileCompletenessScore += 5;
		if (fullUser.kinkCategoryBitmask && fullUser.kinkCategoryBitmask > 0) profileCompletenessScore += 15;

		// 返回用户数据和状态
		return {
			user: fullUser,
			userProfile: {
				hasBasicProfile,
				hasAdvancedProfile,
				profileCompletenessScore: Math.min(profileCompletenessScore, 100)
			}
		};
	} catch (error) {
		console.error('加载用户数据失败:', error);
		throw redirect(303, '/');
	}
};
