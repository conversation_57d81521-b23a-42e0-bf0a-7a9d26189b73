# 里程碑 3 指南：社交与分享功能

**目标:** 实现用户之间的核心交互（喜欢、匹配、拉黑），并构建让用户身份得以在应用外展示的分享机制。这将极大地提升用户粘性和应用的传播潜力。

**前置条件:** 已完成 [里程碑 2: 核心用户体验 - 资料与发现](./MILESTONE-2-CORE-UX.md) 中的所有任务。

---

### 任务 3.1: 实现“喜欢”与“拉黑”的交互逻辑

**目标:** 在用户查看他人资料时，提供“喜欢”和“拉黑”的功能，并将这些关系写入 `matches` 表。

#### **步骤 1: 创建用户资料展示页的路由**

首先，我们需要一个动态路由来展示其他用户的个人资料。

- 创建文件夹 `src/routes/profile/[userId]`。
- `[userId]` 是一个动态参数，代表我们正在查看的用户的ID。

#### **步骤 2: 编写后端 `Actions` 来处理交互**

在 `src/routes/profile/[userId]/+page.server.ts` 文件中，我们将定义 `like` 和 `block` 两个具名 Action。

```typescript
// src/routes/profile/[userId]/+page.server.ts
import { fail, error as skError } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms/server';
import { db } from '$lib/server/db';
import { matches, users } from '$lib/server/db/schema';
import { and, eq, or } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';

// Load 函数负责加载我们正在查看的这个用户的公开资料
export const load: PageServerLoad = async ({ params, locals }) => {
    if (!locals.user) {
        // 未登录用户不能查看他人资料
        throw skError(401, '请先登录');
    }
    const targetUserId = params.userId;

    const targetProfile = await db.query.users.findFirst({
        where: eq(users.id, targetUserId),
        // 出于隐私，只选择性地返回部分字段
        columns: { nickname: true, age: true, profile_image_url: true /* ... 等公开信息 */ }
    });

    if (!targetProfile) {
        throw skError(404, '用户不存在');
    }

    return { targetProfile };
};

export const actions: Actions = {
    // “喜欢”操作
    like: async ({ params, locals }) => {
        const actorUserId = locals.user?.id;
        const targetUserId = params.userId;

        if (!actorUserId) return fail(401);

        // 1. 检查是否已被对方或自己拉黑，如果是，则不允许操作
        // (此逻辑可以按需添加)

        // 2. 在 matches 表中插入或更新一条 'liked' 记录
        // 使用 ON CONFLICT DO NOTHING 避免重复喜欢时报错
        await db.insert(matches).values({
            actorUserId,
            targetUserId,
            status: 'liked'
        }).onConflictDoNothing();

        // 3. 检查对方是否也喜欢我（实现“匹配”逻辑）
        const reverseLike = await db.query.matches.findFirst({
            where: and(
                eq(matches.actorUserId, targetUserId),
                eq(matches.targetUserId, actorUserId),
                eq(matches.status, 'liked')
            )
        });

        if (reverseLike) {
            // 匹配成功！将双方的记录状态都更新为 'matched'
            await db.update(matches).set({ status: 'matched' }).where(
                or(
                    and(eq(matches.actorUserId, actorUserId), eq(matches.targetUserId, targetUserId)),
                    and(eq(matches.actorUserId, targetUserId), eq(matches.targetUserId, actorUserId))
                )
            );
            return { success: true, matched: true }; // 返回匹配成功的信息
        }

        return { success: true, matched: false };
    },

    // “拉黑”操作
    block: async ({ params, locals }) => {
        const actorUserId = locals.user?.id;
        const targetUserId = params.userId;

        if (!actorUserId) return fail(401);

        // 使用 ON CONFLICT DO UPDATE 来确保拉黑操作的最高优先级
        // 无论之前是 'liked' 还是没有记录，都会变成 'blocked'
        await db.insert(matches).values({
            actorUserId,
            targetUserId,
            status: 'blocked'
        }).onConflictOnConstraint('matches_pkey').doUpdate({ // matches_pkey 是组合主键的约束名
            set: { status: 'blocked' }
        });

        return { success: true };
    }
};
步骤 3: 构建前端交互按钮
在 src/routes/profile/[userId]/+page.svelte 中，使用 SvelteKit 的表单 Action 来触发后端逻辑。

Svelte

<script lang="ts">
    import { Button } from 'konsta/svelte';
    import { useEnhance } from 'sveltekit-superforms/client';

    export let data;
    const { targetProfile } = data;
</script>

<h1>{targetProfile.nickname}的资料</h1>
<div class="action-buttons">
    <form method="POST" action="?/block" use:enhance={() => {
        return ({ result }) => {
            if (result.type === 'success') alert('已拉黑');
        }
    }}>
        <Button>拉黑</Button>
    </form>

    <form method="POST" action="?/like" use:enhance={() => {
        return ({ result }) => {
            if (result.type === 'success' && result.data?.matched) {
                alert('恭喜，匹配成功！');
            } else if (result.type === 'success') {
                alert('已喜欢！');
            }
        }
    }}>
        <Button>喜欢</Button>
    </form>
</div>
任务 3.2: 实现“互动”页面 (/interactions)
目标: 创建一个页面，让用户可以看到“谁喜欢了我”和“我的匹配”。

步骤 1: 后端 load 函数提供数据
在 src/routes/interactions/+page.server.ts 中，查询 matches 表来获取两个列表。

TypeScript

// src/routes/interactions/+page.server.ts
import type { PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { matches, users } from '$lib/server/db/schema';
import { and, eq, or } from 'drizzle-orm';

export const load: PageServerLoad = async ({ locals }) => {
    const currentUserId = locals.user?.id;
    if (!currentUserId) return { likesReceived: [], mutualMatches: [] };

    // 1. 查询谁喜欢了我
    const likesReceived = await db.select({
        // ...选择 user 表的公开字段
    })
    .from(matches)
    .innerJoin(users, eq(matches.actorUserId, users.id))
    .where(and(
        eq(matches.targetUserId, currentUserId),
        eq(matches.status, 'liked')
    ));

    // 2. 查询我的匹配 (逻辑较复杂，需要查出与我匹配的另一方)
    // ...

    return { likesReceived, mutualMatches: [] /* 暂留空 */ };
};
步骤 2: 前端使用 Tab 展示
在 src/routes/interactions/+page.svelte 中，用 Konsta UI 的 Tabbar 来分开展示两个列表。

Svelte

<script lang="ts">
    import { Page, Navbar, Tabbar, Tab, Link, Block } from 'konsta/svelte';
    export let data;
    let activeTab = 'likes';
</script>

<Page>
    <Navbar title="我的互动" />

    <Tabbar labels class="top-0">
        <Link tabbar active={activeTab === 'likes'} on:click={() => activeTab = 'likes'}>谁喜欢我</Link>
        <Link tabbar active={activeTab === 'matches'} on:click={() => activeTab = 'matches'}>我的匹配</Link>
    </Tabbar>

    {#if activeTab === 'likes'}
        <Block>
            {#each data.likesReceived as user}
                <p>{user.nickname}</p>
            {:else}
                <p>暂时还没有人喜欢你哦。</p>
            {/each}
        </Block>
    {/if}

    </Page>
任务 3.3: 实现 kink_map_code 的分享页面
目标: 创建一个公开的、只读的 /map/[code] 页面，用于展示用户的 Kink Map。

步骤 1: 后端 load 函数获取数据
TypeScript

// src/routes/map/[code]/+page.server.ts
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';
import { error } from '@sveltejs/kit';
import { decodeRoles } from '$lib/utils/kinkUtils';

export const load: PageServerLoad = async ({ params }) => {
    const kinkMapCode = params.code;
    const userProfile = await db.query.users.findFirst({
        where: eq(users.kinkMapCode, kinkMapCode),
        columns: {
            nickname: true,
            profile_image_url: true,
            kink_category_bitmask: true,
            kink_ratings: true
        }
    });

    if (!userProfile) {
        throw error(404, '未找到该用户的 Kink Map');
    }

    // 在后端直接解码，方便前端使用
    const decodedRoles = decodeRoles(userProfile.kink_category_bitmask);

    return {
        profile: {
            ...userProfile,
            roles: decodedRoles
        }
    };
};
步骤 2: 前端进行可视化展示
在 src/routes/map/[code]/+page.svelte 中，发挥创意，将数据美观地展示出来。

Svelte

<script lang="ts">
    export let data;
    const { profile } = data;
</script>

<div class="kink-map-card">
    <img src={profile.profile_image_url} alt={profile.nickname} />
    <h2>{profile.nickname}的 Kink Map</h2>

    <div class="roles">
        <h3>角色:</h3>
        {#each profile.roles as role}
            <span>{role}</span>
        {/each}
    </div>

    <div class="ratings">
        <h3>偏好:</h3>
        {#each Object.entries(profile.kink_ratings) as [kink, rating]}
            <p>{kink}: {rating}</p>
        {/each}
    </div>
</div>
AI Copilot 指示: 里程碑 3 的任务已规划完毕。请从任务 3.1 开始，为用户资料页添加交互功能。完成后，请记得更新 PROGRESS_LOG.md。
```
