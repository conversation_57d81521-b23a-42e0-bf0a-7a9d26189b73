<script lang="ts">
	import { 
		Page, 
		Navbar, 
		Block, 
		Button 
	} from 'konsta/svelte';

	async function handleTelegramLogin() {
		// 模拟Telegram用户数据（在实际应用中，这些数据会来自Telegram WebApp）
		const mockTelegramUser = {
			id: 123456789,
			first_name: 'Test',
			last_name: 'User',
			username: 'testuser',
			photo_url: null,
			is_premium: false
		};

		try {
			const response = await fetch('/api/auth/telegram', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					telegramUser: mockTelegramUser
				})
			});

			if (response.ok) {
				// 登录成功，刷新页面
				window.location.reload();
			} else {
				console.error('登录失败');
			}
		} catch (error) {
			console.error('登录错误:', error);
		}
	}
</script>

<Page>
	<Navbar title="BlueX" />
	
	<Block class="text-center py-20">
		<div class="text-6xl mb-6">🔐</div>
		<h1 class="text-2xl font-bold mb-4">欢迎来到 BlueX</h1>
		<p class="text-gray-600 dark:text-gray-300 mb-8">
			请先登录以开始您的发现之旅
		</p>
		
		<Button 
			fill 
			onclick={handleTelegramLogin}
		>
			使用 Telegram 登录
		</Button>
	</Block>
</Page>
