<script lang="ts">
	import { Page, Navbar, Block, Button } from 'konsta/svelte';

	async function handleTelegramLogin() {
		// 模拟Telegram用户数据（在实际应用中，这些数据会来自Telegram WebApp）
		const mockTelegramUser = {
			id: 123456789,
			first_name: 'Test',
			last_name: 'User',
			username: 'testuser',
			photo_url: null,
			is_premium: false
		};

		try {
			const response = await fetch('/api/auth/telegram', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					telegramUser: mockTelegramUser
				})
			});

			if (response.ok) {
				// 登录成功，刷新页面
				window.location.reload();
			} else {
				console.error('登录失败');
			}
		} catch (error) {
			console.error('登录错误:', error);
		}
	}

	async function handleQuickLogin() {
		try {
			const response = await fetch('/api/quick-login', {
				method: 'POST'
			});

			if (response.ok) {
				// 登录成功，刷新页面
				window.location.reload();
			} else {
				console.error('快速登录失败');
			}
		} catch (error) {
			console.error('快速登录错误:', error);
		}
	}
</script>

<Page>
	<Navbar title="BlueX" />

	<Block class="py-20 text-center">
		<div class="mb-6 text-6xl">🔐</div>
		<h1 class="mb-4 text-2xl font-bold">欢迎来到 BlueX</h1>
		<p class="mb-8 text-gray-600 dark:text-gray-300">请先登录以开始您的发现之旅</p>

		<div class="space-y-4">
			<Button class="w-full" onclick={handleQuickLogin}>快速登录 (测试)</Button>

			<Button class="w-full" onclick={handleTelegramLogin}>使用 Telegram 登录</Button>
		</div>
	</Block>
</Page>
