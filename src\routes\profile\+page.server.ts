import { redirect } from '@sveltejs/kit';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async ({ locals }) => {
	// 检查用户是否已登录
	if (!locals.user) {
		throw redirect(303, '/');
	}

	try {
		// 从数据库加载完整的用户数据
		const fullUser = await db.query.users.findFirst({
			where: eq(users.id, locals.user.id)
		});

		if (!fullUser) {
			throw redirect(303, '/');
		}

		// 返回完整的用户数据
		return {
			user: fullUser,
			session: locals.session
		};
	} catch (error) {
		console.error('加载用户数据失败:', error);
		throw redirect(303, '/');
	}
};
