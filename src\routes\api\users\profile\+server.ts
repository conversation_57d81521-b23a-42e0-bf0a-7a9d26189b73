import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { basicProfileSchema, fullProfileSchema } from '$lib/schemas/user';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';

export const GET: RequestHandler = async ({ cookies }) => {
	try {
		console.log('🔍 API: 获取用户资料请求');

		const sessionToken = cookies.get('session');
		if (!sessionToken) {
			console.log('❌ API: 未找到session token');
			return json({ error: 'Unauthorized' }, { status: 401 });
		}

		// 从 session token 中提取 telegram user id
		const tokenParts = sessionToken.split('_');
		if (tokenParts.length < 3 || tokenParts[0] !== 'session') {
			console.log('❌ API: 无效的session token格式');
			return json({ error: 'Invalid session token' }, { status: 401 });
		}

		const telegramUserId = parseInt(tokenParts[1]);
		if (isNaN(telegramUserId)) {
			console.log('❌ API: 无效的Telegram用户ID');
			return json({ error: 'Invalid session token' }, { status: 401 });
		}

		console.log('🔍 API: 查询用户，Telegram ID:', telegramUserId);

		// 查找用户
		const existingUsers = await db
			.select()
			.from(users)
			.where(eq(users.telegramUserId, telegramUserId))
			.limit(1);

		console.log('📊 API: 数据库查询结果:', existingUsers.length > 0 ? '找到用户' : '用户不存在');

		const user = existingUsers[0];

		if (!user) {
			console.log('❌ API: 用户不存在');
			return json({ error: 'User not found' }, { status: 404 });
		}

		console.log('✅ API: 用户资料查询成功，用户ID:', user.id);
		console.log('📊 API: 返回用户资料数据');

		return json({
			id: user.id,
			nickname: user.nickname,
			profileCompletenessScore: user.profileCompletenessScore,
			pointBalance: user.pointBalance,
			vipLevel: user.vipLevel,
			isVerified: user.isVerified,
			trustScore: user.trustScore,
			kinkMapCode: user.kinkMapCode,
			hasAvatar: user.hasAvatar,
			age: user.age,
			orientation: user.orientation,
			bodyType: user.bodyType,
			presentationStyle: user.presentationStyle,
			bio: user.bio,
			country: user.country,
			city: user.city,
			profileImageUrl: user.profileImageUrl,
			telegramUserId: user.telegramUserId,
			telegramUsername: user.telegramUsername,
			createdAt: user.createdAt,
			updatedAt: user.updatedAt,
			lastActiveAt: user.lastActiveAt,
			isActive: user.isActive
		});
	} catch (error) {
		console.error('Get profile error:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};

export const PATCH: RequestHandler = async ({ request, cookies }) => {
	try {
		const sessionToken = cookies.get('session');
		if (!sessionToken) {
			return json({ error: 'Unauthorized' }, { status: 401 });
		}

		const body = await request.json();

		// 验证更新数据
		const validation = fullProfileSchema.partial().safeParse(body);
		if (!validation.success) {
			return json(
				{ error: 'Invalid profile data', details: validation.error.errors },
				{ status: 400 }
			);
		}

		// 从 session token 中提取 telegram user id
		const tokenParts = sessionToken.split('_');
		if (tokenParts.length < 3 || tokenParts[0] !== 'session') {
			return json({ error: 'Invalid session token' }, { status: 401 });
		}

		const telegramUserId = parseInt(tokenParts[1]);
		if (isNaN(telegramUserId)) {
			return json({ error: 'Invalid session token' }, { status: 401 });
		}

		// 查找用户
		const existingUsers = await db
			.select()
			.from(users)
			.where(eq(users.telegramUserId, telegramUserId))
			.limit(1);

		const user = existingUsers[0];

		if (!user) {
			return json({ error: 'User not found' }, { status: 404 });
		}

		// 更新用户资料
		const updatedUsers = await db
			.update(users)
			.set({
				...validation.data,
				updatedAt: new Date()
			})
			.where(eq(users.id, user.id))
			.returning();

		const updatedUser = updatedUsers[0];

		return json({
			id: updatedUser.id,
			nickname: updatedUser.nickname,
			profileCompletenessScore: updatedUser.profileCompletenessScore,
			pointBalance: updatedUser.pointBalance,
			vipLevel: updatedUser.vipLevel,
			isVerified: updatedUser.isVerified,
			trustScore: updatedUser.trustScore,
			kinkMapCode: updatedUser.kinkMapCode,
			hasAvatar: updatedUser.hasAvatar,
			age: updatedUser.age,
			orientation: updatedUser.orientation,
			bodyType: updatedUser.bodyType,
			presentationStyle: updatedUser.presentationStyle,
			bio: updatedUser.bio,
			country: updatedUser.country,
			city: updatedUser.city,
			profileImageUrl: updatedUser.profileImageUrl,
			telegramUserId: updatedUser.telegramUserId,
			telegramUsername: updatedUser.telegramUsername,
			createdAt: updatedUser.createdAt,
			updatedAt: updatedUser.updatedAt,
			lastActiveAt: updatedUser.lastActiveAt,
			isActive: updatedUser.isActive
		});
	} catch (error) {
		console.error('Update profile error:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
