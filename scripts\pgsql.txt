-- Kink-Map-TMA 数据库架构 - 最终整合版
-- (包含了 users, matches, point_transactions, sessions, keys 五张表，并统一了ID类型)

-- 预备工作：确保必要的扩展已启用
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-------------------------------------------
-- SECTION 1: ENUM TYPE DEFINITIONS
-------------------------------------------
-- (此处省略所有 ENUM 定义，与您贴出的版本一致，可直接使用)
DROP TYPE IF EXISTS orientation_enum CASCADE;
CREATE TYPE orientation_enum AS ENUM ('straight', 'gay', 'lesbian', 'bisexual', 'asexual', 'demisexual', 'pansexual', 'queer', 'fluid', 'other_orientation', 'prefer_not_to_say_orientation');
DROP TYPE IF EXISTS body_type_enum CASCADE;
CREATE TYPE body_type_enum AS ENUM ('male_body', 'female_body', 'other_body_type');
DROP TYPE IF EXISTS presentation_style_enum CASCADE;
CREATE TYPE presentation_style_enum AS ENUM ('conventional_masculine', 'rugged_masculine', 'feminine', 'androgynous_neutral', 'other_presentation_style');
DROP TYPE IF EXISTS relationship_status_enum CASCADE;
CREATE TYPE relationship_status_enum AS ENUM ('single', 'in_a_relationship', 'complicated', 'open_relationship', 'married', 'polyamorous', 'other_relationship_status', 'prefer_not_to_say_relationship_status');
DROP TYPE IF EXISTS completeness_filter_enum CASCADE;
CREATE TYPE completeness_filter_enum AS ENUM ('has_avatar', 'no_avatar', 'is_profile_complete', 'is_profile_incomplete', 'is_verified', 'is_unverified');
DROP TYPE IF EXISTS match_status_enum CASCADE;
CREATE TYPE match_status_enum AS ENUM ('liked', 'matched', 'blocked');
DROP TYPE IF EXISTS point_transaction_type_enum CASCADE;
CREATE TYPE point_transaction_type_enum AS ENUM ('registration_bonus', 'daily_check_in', 'profile_completion_bonus', 'search_cost', 'super_search_cost', 'super_like_cost', 'invite_bonus', 'top_up', 'system_adjustment');

-------------------------------------------
-- SECTION 2: TABLE DEFINITIONS
-------------------------------------------

-- Table 1: users - 核心用户表
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY, -- 修改为 TEXT 以兼容 Lucia
    telegram_user_id BIGINT UNIQUE NOT NULL,
    telegram_username TEXT,
    kink_map_code TEXT UNIQUE NOT NULL,
    nickname TEXT NOT NULL,
    age INT,
    orientation orientation_enum,
    body_type body_type_enum,
    presentation_style presentation_style_enum,
    bio TEXT,
    height_cm INT,
    weight_kg INT,
    relationship_status relationship_status_enum,
    country TEXT,
    city TEXT,
    profile_image_url TEXT,
    has_avatar BOOLEAN NOT NULL DEFAULT FALSE,
    profile_completeness_score SMALLINT NOT NULL DEFAULT 0,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    trust_score INT NOT NULL DEFAULT 100,
    vip_level SMALLINT NOT NULL DEFAULT 0,
    point_balance INT NOT NULL DEFAULT 0,
    kink_category_bitmask BIGINT NOT NULL DEFAULT 0,
    kink_ratings JSONB NOT NULL DEFAULT '{}'::jsonb,
    block_visibility_from_body_types body_type_enum[],
    block_visibility_from_presentation_styles presentation_style_enum[],
    block_visibility_from_orientations orientation_enum[],
    ton_wallet_address TEXT UNIQUE, -- 新增的 TON 钱包地址字段
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_active_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    is_banned BOOLEAN NOT NULL DEFAULT FALSE
);

-- Table 2: sessions - Lucia 会话表 (新增)
CREATE TABLE IF NOT EXISTS sessions (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    expires_at TIMESTAMPTZ NOT NULL
);

-- Table 3: keys - Lucia 凭证表 (新增)
CREATE TABLE IF NOT EXISTS keys (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    hashed_password TEXT
);

-- Table 4: matches - 用户交互表
CREATE TABLE IF NOT EXISTS matches (
    actor_user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 修改为 TEXT
    target_user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 修改为 TEXT
    PRIMARY KEY (actor_user_id, target_user_id),
    status match_status_enum NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT chk_no_self_match CHECK (actor_user_id <> target_user_id)
);

-- Table 5: point_transactions - 用户积分流水表
CREATE TABLE IF NOT EXISTS point_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(), -- 这个表的ID可以保持UUID，因为它不与其他表关联
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- 修改为 TEXT
    amount INT NOT NULL,
    type point_transaction_type_enum NOT NULL,
    description TEXT,
    reference_id TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-------------------------------------------
-- SECTION 3: INDEX DEFINITIONS
-------------------------------------------
-- (与您贴出的版本一致，可直接使用)
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_telegram_user_id ON users (telegram_user_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_users_kink_map_code ON users (kink_map_code);
CREATE INDEX IF NOT EXISTS idx_users_search_sorting ON users (trust_score DESC, last_active_at DESC);
CREATE INDEX IF NOT EXISTS idx_users_common_filters ON users (age, country, city, presentation_style);
CREATE INDEX IF NOT EXISTS idx_users_kink_ratings_gin ON users USING GIN (kink_ratings);
CREATE INDEX IF NOT EXISTS idx_users_block_body_types_gin ON users USING GIN (block_visibility_from_body_types);
CREATE INDEX IF NOT EXISTS idx_users_block_presentation_styles_gin ON users USING GIN (block_visibility_from_presentation_styles);
CREATE INDEX IF NOT EXISTS idx_users_block_orientations_gin ON users USING GIN (block_visibility_from_orientations);

CREATE INDEX IF NOT EXISTS idx_matches_incoming_interactions ON matches (target_user_id, status);

CREATE INDEX IF NOT EXISTS idx_point_transactions_user_id ON point_transactions (user_id);
CREATE INDEX IF NOT EXISTS idx_point_transactions_type ON point_transactions (type);

-------------------------------------------
-- SECTION 4: (Optional) TRIGGERS
-------------------------------------------
-- (与您贴出的版本一致，可直接使用)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW(); 
   RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at 
BEFORE UPDATE ON users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();