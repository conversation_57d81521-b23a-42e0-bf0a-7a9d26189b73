<script lang="ts">
	import { 
		KINK_ROLES, 
		KINK_ROLE_LABELS, 
		KINK_ROLE_DESCRIPTIONS, 
		KINK_ROLE_GROUPS,
		type KinkRole 
	} from '$lib/constants/kink';
	import { encodeRoles, decodeRoles } from '$lib/utils/kinkUtils';
	import { Badge } from '$lib/components/ui/badge';
	import { Button } from '$lib/components/ui/button';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { Label } from '$lib/components/ui/label';
	import * as Collapsible from '$lib/components/ui/collapsible';
	import { ChevronDown, ChevronUp, Info } from '@lucide/svelte';

	interface Props {
		value?: number; // 位掩码值
		selectedRoles?: KinkRole[]; // 或者直接传角色数组
		onSelectionChange?: (roles: KinkRole[], bitmask: number) => void;
		disabled?: boolean;
		showDescriptions?: boolean;
		maxSelections?: number;
		groupedView?: boolean;
	}

	let {
		value = 0,
		selectedRoles = [],
		onSelectionChange,
		disabled = false,
		showDescriptions = true,
		maxSelections,
		groupedView = true
	}: Props = $props();

	// 内部状态管理
	let internalSelectedRoles = $state<KinkRole[]>([]);
	let expandedGroups = $state<Record<string, boolean>>({});

	// 初始化选中状态
	$effect(() => {
		if (selectedRoles.length > 0) {
			internalSelectedRoles = [...selectedRoles];
		} else if (value > 0) {
			internalSelectedRoles = decodeRoles(value);
		}
	});

	// 监听选择变化
	$effect(() => {
		if (onSelectionChange) {
			const bitmask = encodeRoles(internalSelectedRoles);
			onSelectionChange(internalSelectedRoles, bitmask);
		}
	});

	// Svelte 5 调试
	$inspect('KinkRoleSelector - 选中角色:', internalSelectedRoles);
	$inspect('KinkRoleSelector - 位掩码值:', encodeRoles(internalSelectedRoles));

	function toggleRole(role: KinkRole) {
		if (disabled) return;

		const isSelected = internalSelectedRoles.includes(role);
		
		if (isSelected) {
			// 移除角色
			internalSelectedRoles = internalSelectedRoles.filter(r => r !== role);
		} else {
			// 添加角色（检查最大选择数限制）
			if (maxSelections && internalSelectedRoles.length >= maxSelections) {
				return; // 达到最大选择数，不允许继续选择
			}
			internalSelectedRoles = [...internalSelectedRoles, role];
		}
	}

	function toggleGroup(groupKey: string) {
		expandedGroups[groupKey] = !expandedGroups[groupKey];
	}

	function clearAll() {
		if (disabled) return;
		internalSelectedRoles = [];
	}

	function isRoleSelected(role: KinkRole): boolean {
		return internalSelectedRoles.includes(role);
	}

	function getRoleCount(): string {
		if (maxSelections) {
			return `${internalSelectedRoles.length}/${maxSelections}`;
		}
		return `${internalSelectedRoles.length}`;
	}
</script>

<div class="kink-role-selector space-y-4">
	<!-- 标题和统计 -->
	<div class="flex items-center justify-between">
		<div class="flex items-center gap-2">
			<h3 class="text-lg font-semibold">选择您的角色偏好</h3>
			<Badge variant="secondary">{getRoleCount()}</Badge>
		</div>
		{#if internalSelectedRoles.length > 0}
			<Button variant="outline" size="sm" onclick={clearAll} {disabled}>
				清空选择
			</Button>
		{/if}
	</div>

	<!-- 已选择的角色预览 -->
	{#if internalSelectedRoles.length > 0}
		<div class="selected-roles space-y-2">
			<Label class="text-sm font-medium">已选择的角色：</Label>
			<div class="flex flex-wrap gap-2">
				{#each internalSelectedRoles as role}
					<Badge 
						variant="default" 
						class="cursor-pointer hover:bg-destructive hover:text-destructive-foreground transition-colors"
						onclick={() => toggleRole(role)}
					>
						{KINK_ROLE_LABELS[role]}
						<span class="ml-1 text-xs">×</span>
					</Badge>
				{/each}
			</div>
		</div>
	{/if}

	<!-- 角色选择区域 -->
	<div class="role-selection space-y-4">
		{#if groupedView}
			<!-- 分组视图 -->
			{#each Object.entries(KINK_ROLE_GROUPS) as [groupKey, group]}
				<Collapsible.Root bind:open={expandedGroups[groupKey]}>
					<Collapsible.Trigger asChild let:builder>
						<Button 
							builders={[builder]} 
							variant="ghost" 
							class="w-full justify-between p-3 h-auto"
							onclick={() => toggleGroup(groupKey)}
						>
							<span class="font-medium">{group.label}</span>
							{#if expandedGroups[groupKey]}
								<ChevronUp class="h-4 w-4" />
							{:else}
								<ChevronDown class="h-4 w-4" />
							{/if}
						</Button>
					</Collapsible.Trigger>
					
					<Collapsible.Content class="space-y-2 pt-2">
						{#each group.roles as role}
							<div class="role-item flex items-start space-x-3 p-3 rounded-lg border hover:bg-muted/50 transition-colors">
								<Checkbox
									id={role}
									checked={isRoleSelected(role)}
									onCheckedChange={() => toggleRole(role)}
									{disabled}
								/>
								<div class="flex-1 space-y-1">
									<Label 
										for={role} 
										class="text-sm font-medium cursor-pointer"
									>
										{KINK_ROLE_LABELS[role]}
									</Label>
									{#if showDescriptions}
										<p class="text-xs text-muted-foreground">
											{KINK_ROLE_DESCRIPTIONS[role]}
										</p>
									{/if}
								</div>
							</div>
						{/each}
					</Collapsible.Content>
				</Collapsible.Root>
			{/each}
		{:else}
			<!-- 列表视图 -->
			<div class="grid gap-2">
				{#each Object.keys(KINK_ROLES) as role}
					<div class="role-item flex items-start space-x-3 p-3 rounded-lg border hover:bg-muted/50 transition-colors">
						<Checkbox
							id={role}
							checked={isRoleSelected(role as KinkRole)}
							onCheckedChange={() => toggleRole(role as KinkRole)}
							{disabled}
						/>
						<div class="flex-1 space-y-1">
							<Label 
								for={role} 
								class="text-sm font-medium cursor-pointer"
							>
								{KINK_ROLE_LABELS[role as KinkRole]}
							</Label>
							{#if showDescriptions}
								<p class="text-xs text-muted-foreground">
									{KINK_ROLE_DESCRIPTIONS[role as KinkRole]}
								</p>
							{/if}
						</div>
					</div>
				{/each}
			</div>
		{/if}
	</div>

	<!-- 帮助信息 -->
	{#if maxSelections}
		<div class="help-text flex items-start gap-2 p-3 bg-muted/50 rounded-lg">
			<Info class="h-4 w-4 mt-0.5 text-muted-foreground" />
			<p class="text-xs text-muted-foreground">
				您最多可以选择 {maxSelections} 个角色。选择的角色将用于匹配算法，帮助您找到更合适的伙伴。
			</p>
		</div>
	{/if}
</div>

<style>
	.kink-role-selector {
		max-width: 100%;
	}
	
	.role-item:hover {
		transform: translateY(-1px);
		box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	}
	
	.selected-roles .badge {
		animation: fadeIn 0.2s ease-in-out;
	}
	
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: scale(0.8);
		}
		to {
			opacity: 1;
			transform: scale(1);
		}
	}
</style>
