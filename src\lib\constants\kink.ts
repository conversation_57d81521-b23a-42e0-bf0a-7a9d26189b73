// Kink-related constants and types

/**
 * 5个核心安全的Kink类别 (SSC/RACK原则)
 * 评级范围: -1 (不感兴趣) 到 5 (非常感兴趣)
 */
export const CORE_KINK_CATEGORIES = {
	rope: 'rope', // 绳缚
	foot: 'foot', // 足部
	service: 'service', // 服务
	humiliation: 'humiliation', // 羞辱
	online: 'online' // 线上
} as const;

export type CoreKinkCategory = keyof typeof CORE_KINK_CATEGORIES;

/**
 * Kink类别的中文标签
 */
export const KINK_CATEGORY_LABELS: Record<CoreKinkCategory, string> = {
	rope: '绳缚/束缚',
	foot: '足部崇拜',
	service: '服务导向',
	humiliation: '羞辱调教',
	online: '线上互动'
};

/**
 * Kink类别的详细描述
 */
export const KINK_CATEGORY_DESCRIPTIONS: Record<CoreKinkCategory, string> = {
	rope: '使用绳索、束缚带等工具进行安全的束缚游戏',
	foot: '对足部的崇拜、按摩、护理等相关活动',
	service: '通过服务他人获得满足感的角色扮演',
	humiliation: '在安全、同意的前提下进行的心理调教',
	online: '通过网络进行的各种互动和角色扮演'
};

/**
 * Kink评级的含义
 */
export const KINK_RATING_LABELS = {
	'-1': '不感兴趣',
	'0': '未评价',
	'1': '略有兴趣',
	'2': '有些兴趣',
	'3': '中等兴趣',
	'4': '很感兴趣',
	'5': '非常感兴趣'
} as const;

/**
 * KINK_ROLES 定义了各种kink角色及其对应的位值
 * 使用位掩码可以高效地存储和查询用户的多个角色
 */
export const KINK_ROLES = {
	TOP: 1, // 1 << 0
	BOTTOM: 2, // 1 << 1
	VERSATILE: 4, // 1 << 2
	SIDE: 8, // 1 << 3
	S_ROLE: 16, // 1 << 4 (Sadist)
	M_ROLE: 32, // 1 << 5 (Masochist)
	EDGER: 64, // 1 << 6
	EDGEE: 128, // 1 << 7
	DOMINANT: 256, // 1 << 8
	SUBMISSIVE: 512, // 1 << 9
	SWITCH: 1024, // 1 << 10
	VANILLA: 2048 // 1 << 11
} as const;

export type KinkRole = keyof typeof KINK_ROLES;

/**
 * 为了方便在UI上展示，创建一个标签映射
 */
export const KINK_ROLE_LABELS: Record<KinkRole, string> = {
	TOP: 'Top/攻',
	BOTTOM: 'Bottom/受',
	VERSATILE: 'Versatile/可攻可受',
	SIDE: 'Side/不插入',
	S_ROLE: 'Sadist/施虐',
	M_ROLE: 'Masochist/受虐',
	EDGER: 'Edger/控制高潮',
	EDGEE: 'Edgee/被控制高潮',
	DOMINANT: 'Dominant/支配',
	SUBMISSIVE: 'Submissive/顺从',
	SWITCH: 'Switch/可切换',
	VANILLA: 'Vanilla/传统'
};

/**
 * 角色描述，用于帮助用户理解各个角色的含义
 */
export const KINK_ROLE_DESCRIPTIONS: Record<KinkRole, string> = {
	TOP: '在性行为中处于主动插入的角色',
	BOTTOM: '在性行为中处于被插入的角色',
	VERSATILE: '可以在Top和Bottom之间切换的角色',
	SIDE: '不进行插入性行为，专注于其他形式的亲密接触',
	S_ROLE: '喜欢在BDSM中给予痛苦或控制的角色',
	M_ROLE: '喜欢在BDSM中接受痛苦或被控制的角色',
	EDGER: '喜欢控制伴侣高潮时机的角色',
	EDGEE: '喜欢被控制高潮时机的角色',
	DOMINANT: '在关系中喜欢占主导地位的角色',
	SUBMISSIVE: '在关系中喜欢顺从的角色',
	SWITCH: '可以在支配和顺从之间切换的角色',
	VANILLA: '偏好传统、温和性行为的角色'
};

/**
 * 角色分组，用于在UI中更好地组织显示
 */
export const KINK_ROLE_GROUPS = {
	POSITION: {
		label: '体位偏好',
		roles: ['TOP', 'BOTTOM', 'VERSATILE', 'SIDE'] as KinkRole[]
	},
	BDSM: {
		label: 'BDSM角色',
		roles: ['S_ROLE', 'M_ROLE', 'DOMINANT', 'SUBMISSIVE', 'SWITCH'] as KinkRole[]
	},
	PLAY_STYLE: {
		label: '游戏风格',
		roles: ['EDGER', 'EDGEE', 'VANILLA'] as KinkRole[]
	}
} as const;

/**
 * 获取所有角色的数组
 */
export const ALL_KINK_ROLES = Object.keys(KINK_ROLES) as KinkRole[];

/**
 * 检查两个角色是否兼容（用于匹配算法）
 */
export const COMPATIBLE_ROLES: Record<KinkRole, KinkRole[]> = {
	TOP: ['BOTTOM', 'VERSATILE', 'SUBMISSIVE'],
	BOTTOM: ['TOP', 'VERSATILE', 'DOMINANT'],
	VERSATILE: ['TOP', 'BOTTOM', 'VERSATILE', 'DOMINANT', 'SUBMISSIVE', 'SWITCH'],
	SIDE: ['SIDE', 'VANILLA'],
	S_ROLE: ['M_ROLE', 'SUBMISSIVE', 'SWITCH'],
	M_ROLE: ['S_ROLE', 'DOMINANT', 'SWITCH'],
	EDGER: ['EDGEE', 'SUBMISSIVE'],
	EDGEE: ['EDGER', 'DOMINANT'],
	DOMINANT: ['SUBMISSIVE', 'BOTTOM', 'M_ROLE', 'SWITCH'],
	SUBMISSIVE: ['DOMINANT', 'TOP', 'S_ROLE', 'SWITCH'],
	SWITCH: ['SWITCH', 'VERSATILE', 'S_ROLE', 'M_ROLE', 'DOMINANT', 'SUBMISSIVE'],
	VANILLA: ['VANILLA', 'SIDE']
};

/**
 * Kink评分的范围和含义
 */
export const KINK_RATING_SCALE = {
	'-1': { label: '绝对不要', color: 'red', description: '完全不感兴趣，不愿尝试' },
	'0': { label: '不感兴趣', color: 'gray', description: '目前不感兴趣' },
	'1': { label: '可能尝试', color: 'yellow', description: '在合适的情况下可能会尝试' },
	'2': { label: '有点兴趣', color: 'orange', description: '有一定兴趣，愿意探索' },
	'3': { label: '很感兴趣', color: 'blue', description: '很感兴趣，经常想要' },
	'4': { label: '非常喜欢', color: 'green', description: '非常喜欢，是重要的偏好' },
	'5': { label: '必须要有', color: 'purple', description: '必不可少，是核心需求' }
} as const;

export type KinkRating = keyof typeof KINK_RATING_SCALE;

/**
 * 常见的kink类别，用于评分系统
 */
export const KINK_CATEGORIES = {
	// BDSM相关
	bondage: '束缚',
	discipline: '调教',
	dominance_submission: '支配与顺从',
	sadism_masochism: '施虐与受虐',

	// 角色扮演
	roleplay: '角色扮演',
	age_play: '年龄扮演',
	pet_play: '宠物扮演',

	// 感官刺激
	sensory_play: '感官游戏',
	temperature_play: '温度游戏',
	wax_play: '蜡烛游戏',

	// 其他
	exhibitionism: '暴露癖',
	voyeurism: '窥视癖',
	group_play: '群体游戏',
	public_play: '公共场所游戏'
} as const;

export type KinkCategory = keyof typeof KINK_CATEGORIES;
