<script lang="ts">
	import { onMount } from 'svelte';
	import { 
		Page, 
		Navbar, 
		Block, 
		BlockTitle, 
		Button, 
		List, 
		ListItem 
	} from 'konsta/svelte';

	let authStatus = $state('未知');
	let sessionInfo = $state(null);
	let testResults = $state([]);

	onMount(() => {
		checkAuthStatus();
	});

	async function checkAuthStatus() {
		try {
			const response = await fetch('/api/auth/status');
			if (response.ok) {
				const data = await response.json();
				authStatus = data.authenticated ? '已认证' : '未认证';
				sessionInfo = data;
			} else {
				authStatus = '检查失败';
			}
		} catch (error) {
			authStatus = '错误: ' + error.message;
		}
	}

	async function testTelegramAuth() {
		const mockUser = {
			id: 123456789,
			first_name: 'Test',
			last_name: 'User',
			username: 'testuser',
			photo_url: null,
			is_premium: false
		};

		try {
			const response = await fetch('/api/auth/telegram', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					telegramUser: mockUser
				})
			});

			const result = await response.json();
			
			testResults = [...testResults, {
				test: 'Telegram认证',
				status: response.ok ? '成功' : '失败',
				details: result,
				timestamp: new Date().toLocaleTimeString()
			}];

			await checkAuthStatus();
		} catch (error) {
			testResults = [...testResults, {
				test: 'Telegram认证',
				status: '错误',
				details: error.message,
				timestamp: new Date().toLocaleTimeString()
			}];
		}
	}

	function clearResults() {
		testResults = [];
	}
</script>

<svelte:head>
	<title>认证系统测试 - BlueX</title>
</svelte:head>

<Page>
	<Navbar title="认证系统测试" />

	<BlockTitle>当前认证状态</BlockTitle>
	<Block>
		<div class="text-center">
			<div class="text-2xl mb-2">
				{authStatus === '已认证' ? '🟢' : authStatus === '未认证' ? '🔴' : '⚪'}
			</div>
			<div class="font-semibold">{authStatus}</div>
			{#if sessionInfo}
				<div class="text-sm text-gray-600 mt-2">
					用户ID: {sessionInfo.user?.id || '无'}
				</div>
			{/if}
		</div>
	</Block>

	<BlockTitle>测试操作</BlockTitle>
	<List strongIos insetIos>
		<ListItem>
			<Button 
				slot="content" 
				fill 
				onclick={testTelegramAuth}
			>
				测试Telegram登录
			</Button>
		</ListItem>
		
		<ListItem>
			<Button 
				slot="content" 
				fill 
				color="orange"
				onclick={checkAuthStatus}
			>
				刷新认证状态
			</Button>
		</ListItem>
	</List>

	{#if testResults.length > 0}
		<BlockTitle>测试结果</BlockTitle>
		<List strongIos insetIos>
			{#each testResults as result}
				<ListItem 
					title={result.test}
					after={result.timestamp}
				>
					<div slot="subtitle" class="text-sm">
						<div class="font-semibold {result.status === '成功' ? 'text-green-600' : result.status === '失败' ? 'text-red-600' : 'text-orange-600'}">
							{result.status}
						</div>
						<div class="text-gray-600 mt-1">
							{typeof result.details === 'string' ? result.details : JSON.stringify(result.details, null, 2)}
						</div>
					</div>
				</ListItem>
			{/each}
		</List>

		<Block>
			<Button 
				fill 
				color="gray" 
				onclick={clearResults}
			>
				清空结果
			</Button>
		</Block>
	{/if}
</Page>
