# 里程碑 4 指南：成长与商业化系统

**目标:** 建立一个能激励用户行为的积分系统，并在此基础上实现分层的搜索功能，为未来的 VIP 会员等商业化模式打下坚实基础。

**前置条件:** 已完成 [里程碑 3: 社交与分享功能](./MILESTONE-3-SOCIAL.md) 中的所有任务。

---

### 任务 4.1: 实现积分系统与任务列表

**目标:** 创建一个后端的积分增减服务，并在前端提供一个“任务/成长”页面，让用户可以查看积分、赚取积分。

#### **步骤 1: 创建后端的积分服务逻辑**

为了代码的整洁和可复用性，我们不把积分增减的逻辑散落在各个 `action` 中，而是创建一个专门的“服务”。

- 创建新文件 `src/lib/server/services/points.service.ts`

```typescript
// src/lib/server/services/points.service.ts
import { db } from '$lib/server/db';
import { users, pointTransactions } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { PointTransactionType } from '$lib/types'; // 假设类型定义

/**
 * 为用户增减积分，并记录流水
 * @param userId - 用户ID
 * @param amount - 变动数量 (正为增加, 负为减少)
 * @param type - 交易类型
 * @param description - 交易描述
 */
export async function addPoints(
	userId: string,
	amount: number,
	type: PointTransactionType,
	description: string
) {
	// 使用 Drizzle 的事务功能，确保两步操作要么都成功，要么都失败
	return await db.transaction(async (tx) => {
		// 1. 更新 users 表中的 point_balance
		const updatedUser = await tx
			.update(users)
			.set({
				pointBalance: sql`${users.pointBalance} + ${amount}`
			})
			.where(eq(users.id, userId))
			.returning({ balance: users.pointBalance });

		// 2. 在 point_transactions 表中插入一条流水记录
		await tx.insert(pointTransactions).values({
			userId,
			amount,
			type,
			description
		});

		return updatedUser[0];
	});
}
步骤 2: 创建“成长/任务”页面
这个页面将是用户赚取积分的中心。

创建路由 src/routes/growth。
后端 +page.server.ts:
TypeScript

// src/routes/growth/+page.server.ts
import type { PageServerLoad, Actions } from './$types';
import { db } from '$lib/server/db';
import { pointTransactions } from '$lib/server/db/schema';
import { eq, desc } from 'drizzle-orm';
import { addPoints } from '$lib/server/services/points.service';

export const load: PageServerLoad = async ({ locals }) => {
    const user = locals.user;
    if (!user) return {};

    // 加载最近的10条积分记录
    const history = await db.query.pointTransactions.findMany({
        where: eq(pointTransactions.userId, user.id),
        orderBy: [desc(pointTransactions.createdAt)],
        limit: 10
    });

    // user 对象已包含 pointBalance，无需重复查询
    return { history };
};

export const actions: Actions = {
    // "每日签到" Action
    dailyCheckIn: async ({ locals }) => {
        const user = locals.user;
        if (!user) return { success: false, message: '请先登录' };

        // TODO: 在这里增加逻辑，判断用户今天是否已经签到过
        // (比如查询 point_transactions 表今天是否有 'daily_check_in' 类型的记录)

        await addPoints(user.id, 10, 'daily_check_in', '每日签到奖励');

        return { success: true, message: '签到成功，+10积分！' };
    }
};
前端 +page.svelte:
Svelte

<script lang="ts">
    import { Page, Navbar, BlockTitle, List, ListItem, Button, Block } from 'konsta/svelte';
    import { superForm } from 'sveltekit-superforms/client';

    export let data;
    // 为每日签到创建一个表单实例来处理返回消息
    const { form, enhance } = superForm(data.form);
</script>

<Page>
    <Navbar title="任务与积分" />

    <Block strongIos insetIos class="text-center">
        <p class="text-gray-500">我的积分</p>
        <p class="text-3xl font-bold">{data.user?.pointBalance ?? 0}</p>
    </Block>

    <BlockTitle>任务列表</BlockTitle>
    <List strongIos insetIos>
        <ListItem title="完善个人资料" after="+50 积分" />
        <ListItem header="每日任务">
            <form method="POST" action="?/dailyCheckIn" use:enhance>
                <Button rounded>每日签到 (+10 积分)</Button>
            </form>
            {#if $form.message}<p>{$form.message}</p>{/if}
        </ListItem>
    </List>

    <BlockTitle>最近积分记录</BlockTitle>
    <List strongIos insetIos>
        {#each data.history as item}
            <ListItem title={item.description} after={`${item.amount > 0 ? '+' : ''}${item.amount}`} />
        {/each}
    </List>
</Page>
任务 4.2: 实现高级与 VIP 搜索功能
目标: 升级“发现”页面，根据用户的资料完整度或 VIP 等级，来解锁不同的筛选条件。

步骤 1: 升级后端搜索逻辑
我们需要重构 /discover/+page.server.ts 的 load 函数，让它变得更“智能”。

TypeScript

// src/routes/discover/+page.server.ts
import type { PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { sql } from 'drizzle-orm';

export const load: PageServerLoad = async ({ locals, url }) => {
    const user = locals.user;
    if (!user) return { users: [] };

    const searchParams = url.searchParams;

    // 构建动态的 WHERE 查询条件
    const conditions = [sql`"users"."id" != ${user.id}`]; // 不显示自己

    // 基础筛选条件
    if (searchParams.has('age_min')) {
        conditions.push(sql`"users"."age" >= ${searchParams.get('age_min')}`);
    }
    // ... 其他基础条件

    // -- 权限控制逻辑在这里！ --
    const canUseAdvancedSearch = (user.profileCompletenessScore >= 80) || (user.vipLevel > 0);

    if (canUseAdvancedSearch) {
        // 只有满足条件的用户，他们的这些高级筛选参数才会生效
        if (searchParams.has('presentation_style')) {
            conditions.push(sql`"users"."presentation_style" = ${searchParams.get('presentation_style')}`);
        }
        // ... 其他高级条件
    }

    const userList = await db.select().from(users).where(sql.join(conditions, sql.raw(' AND ')));

    return { users: userList, canUseAdvancedSearch }; // 把权限也返回给前端
};
步骤 2: 升级前端搜索界面
前端需要根据 data.canUseAdvancedSearch 的值，来决定是否显示或启用高级筛选控件。

Svelte

<script lang="ts">
    import { Select, Range, Button, Block } from 'konsta/svelte';
    export let data;
</script>

<Block>
    <form method="GET">
        <Range label="年龄范围" ... />

        {#if data.canUseAdvancedSearch}
            <Select label="呈现风格 (高级)" name="presentation_style">
                <option value="conventional_masculine">常规男性化</option>
                </Select>
        {:else}
            <div class="locked-feature">
                <p>完善资料至80%或成为VIP，即可解锁高级筛选功能，发现更精准的匹配！</p>
                <Button small rounded href="/onboarding">去完善资料</Button>
                <Button small rounded href="/vip">了解VIP</Button>
            </div>
        {/if}

        <Button type="submit">筛选</Button>
    </form>
</Block>

完成这个里程碑后，您的应用就不仅仅是一个简单的社交工具了。它拥有了用户激励机制和价值分层，为下一步的用户增长和商业化探索铺平了坚实的道路。

AI Copilot 指示: 里程碑 4 的任务已规划完毕。请从任务 4.1 开始，构建积分服务和任务页面。完成后，请记得更新 PROGRESS_LOG.md。
```
