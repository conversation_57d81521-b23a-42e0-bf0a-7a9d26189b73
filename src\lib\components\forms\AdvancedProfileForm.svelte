<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { advancedProfileSchema, type AdvancedProfile } from '$lib/schemas/profile';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import * as Select from '$lib/components/ui/select';
	import * as Card from '$lib/components/ui/card';
	import { Separator } from '$lib/components/ui/separator';
	import KinkRoleSelector from './KinkRoleSelector.svelte';
	import { Ruler, Weight, Heart, Shield, EyeOff, ArrowLeft, Save } from '@lucide/svelte';

	interface Props {
		data: any;
		onSubmit?: (data: AdvancedProfile) => void;
		onCancel?: () => void;
		isLoading?: boolean;
	}

	let { data, onSubmit, onCancel, isLoading = false }: Props = $props();

	const { form, errors, enhance, submitting } = superForm(data, {
		validators: zodClient(advancedProfileSchema),
		onUpdated: ({ form }) => {
			if (form.valid && onSubmit) {
				onSubmit(form.data);
			}
		}
	});

	// Svelte 5 调试
	$inspect('AdvancedProfileForm - 表单数据:', $form);
	$inspect('AdvancedProfileForm - 表单错误:', $errors);

	// 关系状态选项
	const relationshipStatusOptions = [
		{ value: 'single', label: '单身' },
		{ value: 'in_a_relationship', label: '恋爱中' },
		{ value: 'complicated', label: '复杂关系' },
		{ value: 'open_relationship', label: '开放关系' },
		{ value: 'married', label: '已婚' },
		{ value: 'polyamorous', label: '多元关系' },
		{ value: 'other_relationship_status', label: '其他' },
		{ value: 'prefer_not_to_say_relationship_status', label: '不愿透露' }
	];

	// 身体类型选项（用于隐私设置）
	const bodyTypeOptions = [
		{ value: 'male_body', label: '男性身体' },
		{ value: 'female_body', label: '女性身体' },
		{ value: 'other_body_type', label: '其他身体类型' }
	];

	// 展示风格选项（用于隐私设置）
	const presentationStyleOptions = [
		{ value: 'conventional_masculine', label: '传统男性化' },
		{ value: 'rugged_masculine', label: '粗犷男性化' },
		{ value: 'feminine', label: '女性化' },
		{ value: 'androgynous_neutral', label: '中性/雌雄同体' },
		{ value: 'other_presentation_style', label: '其他风格' }
	];

	// 性取向选项（用于隐私设置）
	const orientationOptions = [
		{ value: 'straight', label: '异性恋' },
		{ value: 'gay', label: '男同性恋' },
		{ value: 'lesbian', label: '女同性恋' },
		{ value: 'bisexual', label: '双性恋' },
		{ value: 'asexual', label: '无性恋' },
		{ value: 'demisexual', label: '半性恋' },
		{ value: 'pansexual', label: '泛性恋' },
		{ value: 'queer', label: '酷儿' },
		{ value: 'fluid', label: '流动性' },
		{ value: 'other_orientation', label: '其他' },
		{ value: 'prefer_not_to_say_orientation', label: '不愿透露' }
	];

	// Kink角色选择处理
	function handleKinkRoleChange(roles: any[], bitmask: number) {
		$form.kinkRoles = roles;
	}
</script>

<Card.Root class="mx-auto w-full max-w-2xl">
	<Card.Header class="text-center">
		<Card.Title class="flex items-center justify-center gap-2 text-2xl font-bold">
			<Heart class="text-primary h-6 w-6" />
			高级资料设置
		</Card.Title>
		<Card.Description>完善您的详细信息和偏好设置，获得更精准的匹配推荐</Card.Description>
	</Card.Header>

	<Card.Content>
		<form method="POST" use:enhance class="space-y-8">
			<!-- 身体信息 -->
			<div class="space-y-4">
				<div class="flex items-center gap-2">
					<Ruler class="text-muted-foreground h-5 w-5" />
					<h3 class="text-lg font-semibold">身体信息</h3>
				</div>

				<div class="grid grid-cols-1 gap-4 md:grid-cols-2">
					<!-- 身高 -->
					<div class="space-y-2">
						<Label for="heightCm">身高 (cm)</Label>
						<Input
							id="heightCm"
							name="heightCm"
							type="number"
							placeholder="例如: 175"
							bind:value={$form.heightCm}
							disabled={$submitting || isLoading}
							class={$errors.heightCm ? 'border-red-500' : ''}
						/>
						{#if $errors.heightCm}
							<p class="text-sm text-red-500">{$errors.heightCm}</p>
						{/if}
					</div>

					<!-- 体重 -->
					<div class="space-y-2">
						<Label for="weightKg" class="flex items-center gap-2">
							<Weight class="h-4 w-4" />
							体重 (kg)
						</Label>
						<Input
							id="weightKg"
							name="weightKg"
							type="number"
							placeholder="例如: 70"
							bind:value={$form.weightKg}
							disabled={$submitting || isLoading}
							class={$errors.weightKg ? 'border-red-500' : ''}
						/>
						{#if $errors.weightKg}
							<p class="text-sm text-red-500">{$errors.weightKg}</p>
						{/if}
					</div>
				</div>
			</div>

			<Separator />

			<!-- 关系状态 -->
			<div class="space-y-4">
				<div class="flex items-center gap-2">
					<Heart class="text-muted-foreground h-5 w-5" />
					<h3 class="text-lg font-semibold">关系状态</h3>
				</div>

				<div class="space-y-2">
					<Label for="relationshipStatus">当前关系状态</Label>
					<Select.Root bind:selected={$form.relationshipStatus}>
						<Select.Trigger class={$errors.relationshipStatus ? 'border-red-500' : ''}>
							<Select.Value placeholder="选择您的关系状态" />
						</Select.Trigger>
						<Select.Content>
							{#each relationshipStatusOptions as option}
								<Select.Item value={option.value}>{option.label}</Select.Item>
							{/each}
						</Select.Content>
					</Select.Root>
					{#if $errors.relationshipStatus}
						<p class="text-sm text-red-500">{$errors.relationshipStatus}</p>
					{/if}
				</div>
			</div>

			<Separator />

			<!-- Kink 偏好设置 -->
			<div class="space-y-4">
				<div class="flex items-center gap-2">
					<Heart class="text-muted-foreground h-5 w-5" />
					<h3 class="text-lg font-semibold">偏好设置</h3>
				</div>

				<KinkRoleSelector
					value={0}
					onSelectionChange={handleKinkRoleChange}
					disabled={$submitting || isLoading}
					maxSelections={8}
					showDescriptions={true}
				/>
			</div>

			<Separator />

			<!-- 隐私设置 -->
			<div class="space-y-4">
				<div class="flex items-center gap-2">
					<Shield class="text-muted-foreground h-5 w-5" />
					<h3 class="text-lg font-semibold">隐私设置</h3>
				</div>
				<p class="text-muted-foreground text-sm">
					选择您不希望看到的用户类型，这些用户将不会出现在您的搜索结果中
				</p>

				<!-- 屏蔽身体类型 -->
				<div class="space-y-2">
					<Label class="flex items-center gap-2">
						<EyeOff class="h-4 w-4" />
						屏蔽的身体类型
					</Label>
					<div class="grid grid-cols-1 gap-2">
						{#each bodyTypeOptions as option}
							<label class="flex items-center space-x-2">
								<input
									type="checkbox"
									bind:group={$form.blockVisibilityFromBodyTypes}
									value={option.value}
									disabled={$submitting || isLoading}
								/>
								<span class="text-sm">{option.label}</span>
							</label>
						{/each}
					</div>
				</div>

				<!-- 屏蔽展示风格 -->
				<div class="space-y-2">
					<Label>屏蔽的展示风格</Label>
					<div class="grid grid-cols-1 gap-2">
						{#each presentationStyleOptions as option}
							<label class="flex items-center space-x-2">
								<input
									type="checkbox"
									bind:group={$form.blockVisibilityFromPresentationStyles}
									value={option.value}
									disabled={$submitting || isLoading}
								/>
								<span class="text-sm">{option.label}</span>
							</label>
						{/each}
					</div>
				</div>

				<!-- 屏蔽性取向 -->
				<div class="space-y-2">
					<Label>屏蔽的性取向</Label>
					<div class="grid grid-cols-1 gap-2">
						{#each orientationOptions as option}
							<label class="flex items-center space-x-2">
								<input
									type="checkbox"
									bind:group={$form.blockVisibilityFromOrientations}
									value={option.value}
									disabled={$submitting || isLoading}
								/>
								<span class="text-sm">{option.label}</span>
							</label>
						{/each}
					</div>
				</div>
			</div>

			<!-- 操作按钮 -->
			<div class="flex flex-col gap-3 pt-6 sm:flex-row">
				{#if onCancel}
					<Button
						type="button"
						variant="outline"
						size="lg"
						class="flex-1"
						onclick={onCancel}
						disabled={$submitting || isLoading}
					>
						<ArrowLeft class="mr-2 h-4 w-4" />
						稍后设置
					</Button>
				{/if}

				<Button type="submit" size="lg" class="flex-1" disabled={$submitting || isLoading}>
					{#if $submitting || isLoading}
						<div class="mr-2 h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
						保存中...
					{:else}
						<Save class="mr-2 h-4 w-4" />
						保存设置
					{/if}
				</Button>
			</div>
		</form>
	</Card.Content>
</Card.Root>
