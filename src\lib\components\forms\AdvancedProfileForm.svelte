<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { advancedProfileSchema, type AdvancedProfile } from '$lib/schemas/profile';
	import {
		Page,
		Navbar,
		Block,
		BlockTitle,
		Button,
		List,
		ListInput,
		ListItem,
		Checkbox
	} from 'konsta/svelte';
	import KinkRoleSelector from './KinkRoleSelector.svelte';

	interface Props {
		data: any;
		onSubmit?: (data: AdvancedProfile) => void;
		onCancel?: () => void;
		isLoading?: boolean;
	}

	let { data, onSubmit, onCancel, isLoading = false }: Props = $props();

	const { form, errors, enhance, submitting } = superForm(data, {
		validators: zodClient(advancedProfileSchema),
		onUpdated: ({ form }) => {
			if (form.valid && onSubmit) {
				onSubmit(form.data);
			}
		}
	});

	// Svelte 5 调试
	$inspect('AdvancedProfileForm - 表单数据:', $form);
	$inspect('AdvancedProfileForm - 表单错误:', $errors);

	// 关系状态选项
	const relationshipStatusOptions = [
		{ value: 'single', label: '单身' },
		{ value: 'in_a_relationship', label: '恋爱中' },
		{ value: 'complicated', label: '复杂关系' },
		{ value: 'open_relationship', label: '开放关系' },
		{ value: 'married', label: '已婚' },
		{ value: 'polyamorous', label: '多元关系' },
		{ value: 'other_relationship_status', label: '其他' },
		{ value: 'prefer_not_to_say_relationship_status', label: '不愿透露' }
	];

	// 身体类型选项（用于隐私设置）
	const bodyTypeOptions = [
		{ value: 'male_body', label: '男性身体' },
		{ value: 'female_body', label: '女性身体' },
		{ value: 'other_body_type', label: '其他身体类型' }
	];

	// 展示风格选项（用于隐私设置）
	const presentationStyleOptions = [
		{ value: 'conventional_masculine', label: '传统男性化' },
		{ value: 'rugged_masculine', label: '粗犷男性化' },
		{ value: 'feminine', label: '女性化' },
		{ value: 'androgynous_neutral', label: '中性/雌雄同体' },
		{ value: 'other_presentation_style', label: '其他风格' }
	];

	// 性取向选项（用于隐私设置）
	const orientationOptions = [
		{ value: 'straight', label: '异性恋' },
		{ value: 'gay', label: '男同性恋' },
		{ value: 'lesbian', label: '女同性恋' },
		{ value: 'bisexual', label: '双性恋' },
		{ value: 'asexual', label: '无性恋' },
		{ value: 'demisexual', label: '半性恋' },
		{ value: 'pansexual', label: '泛性恋' },
		{ value: 'queer', label: '酷儿' },
		{ value: 'fluid', label: '流动性' },
		{ value: 'other_orientation', label: '其他' },
		{ value: 'prefer_not_to_say_orientation', label: '不愿透露' }
	];

	// Kink角色选择处理
	function handleKinkRoleChange(roles: any[], bitmask: number) {
		$form.kinkRoles = roles;
	}
</script>

<div class="mx-auto max-w-2xl">
	<form method="POST" use:enhance>
		<BlockTitle>高级资料设置</BlockTitle>
		<Block class="mb-4 text-center">
			<p class="text-gray-600 dark:text-gray-300">
				完善您的详细信息和偏好设置，获得更精准的匹配推荐
			</p>
		</Block>

		<!-- 身体信息 -->
		<BlockTitle>身体信息</BlockTitle>
		<List strongIos insetIos>
			<ListInput
				label="身高 (cm)"
				type="number"
				placeholder="例如: 175"
				name="heightCm"
				bind:value={$form.heightCm}
				error={$errors.heightCm}
				disabled={$submitting || isLoading}
			/>
			<ListInput
				label="体重 (kg)"
				type="number"
				placeholder="例如: 70"
				name="weightKg"
				bind:value={$form.weightKg}
				error={$errors.weightKg}
				disabled={$submitting || isLoading}
			/>
		</List>

		<!-- 关系状态 -->
		<BlockTitle>关系状态</BlockTitle>
		<List strongIos insetIos>
			<ListItem
				title="当前关系状态"
				after={$form.relationshipStatus
					? relationshipStatusOptions.find((o) => o.value === $form.relationshipStatus)?.label
					: '请选择'}
				link
			>
				<select
					bind:value={$form.relationshipStatus}
					class="absolute inset-0 h-full w-full opacity-0"
					disabled={$submitting || isLoading}
				>
					<option value="">请选择关系状态</option>
					{#each relationshipStatusOptions as option}
						<option value={option.value}>{option.label}</option>
					{/each}
				</select>
			</ListItem>
			{#if $errors.relationshipStatus}
				<Block class="text-sm text-red-500">{$errors.relationshipStatus}</Block>
			{/if}
		</List>

		<!-- Kink 偏好设置 -->
		<BlockTitle>偏好设置</BlockTitle>
		<Block>
			<KinkRoleSelector
				value={0}
				onSelectionChange={handleKinkRoleChange}
				disabled={$submitting || isLoading}
				maxSelections={8}
				showDescriptions={true}
			/>
		</Block>

		<!-- 隐私设置 -->
		<BlockTitle>隐私设置</BlockTitle>
		<Block class="mb-4 text-sm text-gray-600 dark:text-gray-300">
			<p>选择您不希望看到的用户类型，这些用户将不会出现在您的搜索结果中</p>
		</Block>

		<!-- 屏蔽身体类型 -->
		<List strongIos insetIos>
			<ListItem title="屏蔽的身体类型" groupTitle />
			{#each bodyTypeOptions as option}
				<ListItem title={option.label}>
					<Checkbox
						slot="content"
						bind:checked={$form.blockVisibilityFromBodyTypes}
						value={option.value}
						disabled={$submitting || isLoading}
					/>
				</ListItem>
			{/each}
		</List>

		<!-- 屏蔽展示风格 -->
		<List strongIos insetIos>
			<ListItem title="屏蔽的展示风格" groupTitle />
			{#each presentationStyleOptions as option}
				<ListItem title={option.label}>
					<Checkbox
						slot="content"
						bind:checked={$form.blockVisibilityFromPresentationStyles}
						value={option.value}
						disabled={$submitting || isLoading}
					/>
				</ListItem>
			{/each}
		</List>

		<!-- 屏蔽性取向 -->
		<List strongIos insetIos>
			<ListItem title="屏蔽的性取向" groupTitle />
			{#each orientationOptions as option}
				<ListItem title={option.label}>
					<Checkbox
						slot="content"
						bind:checked={$form.blockVisibilityFromOrientations}
						value={option.value}
						disabled={$submitting || isLoading}
					/>
				</ListItem>
			{/each}
		</List>

		<!-- 操作按钮 -->
		<Block class="flex space-x-3 pt-4">
			{#if onCancel}
				<Button
					type="button"
					outline
					class="flex-1"
					onclick={onCancel}
					disabled={$submitting || isLoading}
				>
					稍后设置
				</Button>
			{/if}

			<Button type="submit" class="flex-1" disabled={$submitting || isLoading}>
				{#if $submitting || isLoading}
					保存中...
				{:else}
					保存设置
				{/if}
			</Button>
		</Block>
	</form>
</div>
