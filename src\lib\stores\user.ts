// User profile store

import { writable, derived } from 'svelte/store';
import type { UserProfile, UserOnboardingState, UserRegistrationStatus } from '../types';
import {
	getUserRegistrationStatus,
	getNextProfileStep,
	calculateProfileCompleteness
} from '../utils/helpers';

interface UserState {
	profile: UserProfile | null;
	onboarding: UserOnboardingState;
	isLoading: boolean;
	error: string | null;
}

const initialOnboardingState: UserOnboardingState = {
	status: 'new_user',
	completedSteps: [],
	currentStep: undefined,
	isFirstTime: true,
	needsAdvancedSetup: false
};

const initialState: UserState = {
	profile: null,
	onboarding: initialOnboardingState,
	isLoading: false,
	error: null
};

export const userStore = writable<UserState>(initialState);

// Derived stores
export const userProfile = derived(userStore, ($user) => {
	console.log('UserStore - 用户资料:', $user.profile);
	return $user.profile;
});

export const userOnboarding = derived(userStore, ($user) => {
	console.log('UserStore - Onboarding状态:', $user.onboarding);
	return $user.onboarding;
});

export const userLoading = derived(userStore, ($user) => $user.isLoading);
export const userError = derived(userStore, ($user) => $user.error);

export const registrationStatus = derived(userProfile, ($profile) => {
	if (!$profile) return 'new_user';
	return getUserRegistrationStatus($profile);
});

export const profileCompleteness = derived(userProfile, ($profile) => {
	if (!$profile) return 0;
	return calculateProfileCompleteness($profile);
});

export const nextProfileStep = derived(userProfile, ($profile) => {
	if (!$profile) return 'nickname';
	return getNextProfileStep($profile);
});

export const canUseBasicSearch = derived(registrationStatus, ($status) => {
	return $status !== 'new_user';
});

export const canUseAdvancedSearch = derived(registrationStatus, ($status) => {
	return $status === 'advanced_complete' || $status === 'fully_complete';
});

export const canUseSuperSearch = derived(userProfile, ($profile) => {
	return $profile?.vipLevel && $profile.vipLevel > 0;
});

// Actions
export const userActions = {
	/**
	 * 通过Telegram用户ID加载用户资料
	 */
	async loadProfileByTelegramId(telegramUserId: number) {
		userStore.update((state) => ({ ...state, isLoading: true, error: null }));

		try {
			console.log('🔍 加载用户资料，Telegram ID:', telegramUserId);
			const response = await fetch(`/api/users/profile`);

			if (!response.ok) {
				if (response.status === 404) {
					// 用户不存在，这是新用户
					console.log('👤 新用户，设置onboarding状态');
					userStore.update((state) => ({
						...state,
						profile: null,
						onboarding: {
							...state.onboarding,
							status: 'new_user',
							currentStep: 'nickname',
							isFirstTime: true
						},
						isLoading: false,
						error: null
					}));
					return { isNewUser: true };
				}
				throw new Error('加载用户资料失败');
			}

			const profile: UserProfile = await response.json();
			console.log('✅ 用户资料加载成功:', profile);

			// 更新 onboarding 状态
			const status = getUserRegistrationStatus(profile);
			const nextStep = getNextProfileStep(profile);

			userStore.update((state) => ({
				...state,
				profile,
				onboarding: {
					...state.onboarding,
					status,
					currentStep: nextStep,
					isFirstTime: false
				},
				isLoading: false,
				error: null
			}));

			return { isNewUser: false, profile };
		} catch (error) {
			console.error('❌ 加载用户资料失败:', error);
			userStore.update((state) => ({
				...state,
				isLoading: false,
				error: error instanceof Error ? error.message : '加载失败'
			}));
			throw error;
		}
	},

	/**
	 * 加载用户资料（兼容旧方法）
	 */
	async loadProfile(userId: string) {
		userStore.update((state) => ({ ...state, isLoading: true, error: null }));

		try {
			const response = await fetch(`/api/users/${userId}`);

			if (!response.ok) {
				throw new Error('加载用户资料失败');
			}

			const profile: UserProfile = await response.json();

			// 更新 onboarding 状态
			const status = getUserRegistrationStatus(profile);
			const nextStep = getNextProfileStep(profile);

			userStore.update((state) => ({
				...state,
				profile,
				onboarding: {
					...state.onboarding,
					status,
					currentStep: nextStep,
					isFirstTime: false
				},
				isLoading: false,
				error: null
			}));
		} catch (error) {
			userStore.update((state) => ({
				...state,
				isLoading: false,
				error: error instanceof Error ? error.message : '加载失败'
			}));
		}
	},

	/**
	 * 更新用户资料
	 */
	async updateProfile(updates: Partial<UserProfile>) {
		userStore.update((state) => ({ ...state, isLoading: true, error: null }));

		try {
			const response = await fetch('/api/users/profile', {
				method: 'PATCH',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify(updates)
			});

			if (!response.ok) {
				throw new Error('更新用户资料失败');
			}

			const updatedProfile: UserProfile = await response.json();

			// 重新计算 onboarding 状态
			const status = getUserRegistrationStatus(updatedProfile);
			const nextStep = getNextProfileStep(updatedProfile);

			userStore.update((state) => ({
				...state,
				profile: updatedProfile,
				onboarding: {
					...state.onboarding,
					status,
					currentStep: nextStep
				},
				isLoading: false,
				error: null
			}));

			return { success: true };
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : '更新失败';

			userStore.update((state) => ({
				...state,
				isLoading: false,
				error: errorMessage
			}));

			return { success: false, error: errorMessage };
		}
	},

	/**
	 * 完成 onboarding 步骤
	 */
	completeOnboardingStep(step: string) {
		userStore.update((state) => {
			const completedSteps = [...state.onboarding.completedSteps];
			if (!completedSteps.includes(step as any)) {
				completedSteps.push(step as any);
			}

			return {
				...state,
				onboarding: {
					...state.onboarding,
					completedSteps
				}
			};
		});
	},

	/**
	 * 设置需要高级设置
	 */
	setNeedsAdvancedSetup(needs: boolean) {
		userStore.update((state) => ({
			...state,
			onboarding: {
				...state.onboarding,
				needsAdvancedSetup: needs
			}
		}));
	},

	/**
	 * 重置 onboarding 状态
	 */
	resetOnboarding() {
		userStore.update((state) => ({
			...state,
			onboarding: initialOnboardingState
		}));
	},

	/**
	 * 清除错误
	 */
	clearError() {
		userStore.update((state) => ({ ...state, error: null }));
	},

	/**
	 * 清除用户数据
	 */
	clear() {
		userStore.set(initialState);
	}
};
