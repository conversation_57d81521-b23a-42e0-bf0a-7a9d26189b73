<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { basicProfileSchema, type BasicProfile } from '$lib/schemas/user';
	import { ORIENTATIONS, BODY_TYPES, PRESENTATION_STYLES } from '$lib/utils/constants';
	import { Button } from '$lib/components/ui/button';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';
	import * as Select from '$lib/components/ui/select';
	import { User, Calendar, Heart, Users } from '@lucide/svelte';

	interface Props {
		data: any;
		onSubmit?: (data: BasicProfile) => void;
		onCancel?: () => void;
		isLoading?: boolean;
	}

	let { data, onSubmit, onCancel, isLoading = false }: Props = $props();

	const { form, errors, enhance, submitting } = superForm(data, {
		validators: zodClient(basicProfileSchema),
		onUpdated: ({ form }) => {
			if (form.valid && onSubmit) {
				onSubmit(form.data);
			}
		}
	});

	// Svelte 5 调试 - 监控表单状态
	$inspect('BasicProfileForm - 表单数据:', $form);
	$inspect('BasicProfileForm - 表单错误:', $errors);
	$inspect('BasicProfileForm - 提交状态:', $submitting);

	// 转换选项格式
	const orientationOptions = ORIENTATIONS.map((o) => ({ value: o.value, label: o.label }));
	const bodyTypeOptions = BODY_TYPES.map((b) => ({ value: b.value, label: b.label }));
	const presentationStyleOptions = PRESENTATION_STYLES.map((p) => ({
		value: p.value,
		label: p.label
	}));

	// Select 组件的状态 - 直接绑定到 form 值
	// bits-ui Select 使用 bind:value，不需要额外的状态管理
</script>

<div class="mx-auto max-w-md rounded-lg bg-white p-6 shadow-lg dark:bg-gray-800">
	<div class="mb-6 text-center">
		<div
			class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900"
		>
			<User class="h-8 w-8 text-blue-600 dark:text-blue-400" />
		</div>
		<h2 class="mb-2 text-2xl font-bold text-gray-900 dark:text-white">完善基础资料</h2>
		<p class="text-sm text-gray-600 dark:text-gray-300">这些信息将帮助我们为您提供更好的匹配体验</p>
	</div>

	<form method="POST" use:enhance class="space-y-6">
		<!-- 昵称 -->
		<div class="space-y-2">
			<Label for="nickname" class="flex items-center gap-2">
				<User class="h-4 w-4" />
				昵称 *
			</Label>
			<Input
				id="nickname"
				name="nickname"
				type="text"
				placeholder="输入您的昵称"
				bind:value={$form.nickname}
				disabled={$submitting || isLoading}
				class={$errors.nickname ? 'border-red-500' : ''}
			/>
			{#if $errors.nickname}
				<p class="text-sm text-red-500">{$errors.nickname}</p>
			{/if}
		</div>

		<!-- 年龄 -->
		<div class="space-y-2">
			<Label for="age" class="flex items-center gap-2">
				<Calendar class="h-4 w-4" />
				年龄 *
			</Label>
			<Input
				id="age"
				name="age"
				type="number"
				placeholder="18"
				min="18"
				max="99"
				bind:value={$form.age}
				disabled={$submitting || isLoading}
				class={$errors.age ? 'border-red-500' : ''}
			/>
			{#if $errors.age}
				<p class="text-sm text-red-500">{$errors.age}</p>
			{/if}
		</div>

		<!-- 性取向 -->
		<div class="space-y-2">
			<Label for="orientation" class="flex items-center gap-2">
				<Heart class="h-4 w-4" />
				性取向 *
			</Label>
			<Select.Root bind:value={$form.orientation} disabled={$submitting || isLoading} type="single">
				<Select.Trigger class={$errors.orientation ? 'border-red-500' : ''}>
					{$form.orientation
						? orientationOptions.find((o) => o.value === $form.orientation)?.label
						: '请选择您的性取向'}
				</Select.Trigger>
				<Select.Content>
					{#each orientationOptions as option}
						<Select.Item value={option.value}>
							{option.label}
						</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
			{#if $errors.orientation}
				<p class="text-sm text-red-500">{$errors.orientation}</p>
			{/if}
		</div>

		<!-- 身体类型 -->
		<div class="space-y-2">
			<Label for="bodyType" class="flex items-center gap-2">
				<Users class="h-4 w-4" />
				身体类型 *
			</Label>
			<Select.Root bind:value={$form.bodyType} disabled={$submitting || isLoading} type="single">
				<Select.Trigger class={$errors.bodyType ? 'border-red-500' : ''}>
					{$form.bodyType
						? bodyTypeOptions.find((b) => b.value === $form.bodyType)?.label
						: '请选择您的身体类型'}
				</Select.Trigger>
				<Select.Content>
					{#each bodyTypeOptions as option}
						<Select.Item value={option.value}>
							{option.label}
						</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
			{#if $errors.bodyType}
				<p class="text-sm text-red-500">{$errors.bodyType}</p>
			{/if}
		</div>

		<!-- 展现风格 -->
		<div class="space-y-2">
			<Label for="presentationStyle" class="flex items-center gap-2">
				<Users class="h-4 w-4" />
				展现风格 *
			</Label>
			<Select.Root
				bind:value={$form.presentationStyle}
				disabled={$submitting || isLoading}
				type="single"
			>
				<Select.Trigger class={$errors.presentationStyle ? 'border-red-500' : ''}>
					{$form.presentationStyle
						? presentationStyleOptions.find((p) => p.value === $form.presentationStyle)?.label
						: '请选择您的展现风格'}
				</Select.Trigger>
				<Select.Content>
					{#each presentationStyleOptions as option}
						<Select.Item value={option.value}>
							{option.label}
						</Select.Item>
					{/each}
				</Select.Content>
			</Select.Root>
			{#if $errors.presentationStyle}
				<p class="text-sm text-red-500">{$errors.presentationStyle}</p>
			{/if}
		</div>

		<!-- 隐私提醒 -->
		<div class="rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
			<div class="flex items-start space-x-2">
				<div class="text-lg text-blue-500">🛡️</div>
				<div>
					<h4 class="text-sm font-medium text-blue-900 dark:text-blue-100">隐私保护</h4>
					<p class="mt-1 text-xs text-blue-700 dark:text-blue-300">
						您的所有信息都是绝对隐私的，仅用于匹配算法，不会泄露给任何第三方。
					</p>
				</div>
			</div>
		</div>

		<!-- 操作按钮 -->
		<div class="flex space-x-3 pt-4">
			{#if onCancel}
				<Button
					type="button"
					variant="outline"
					class="flex-1"
					onclick={onCancel}
					disabled={$submitting || isLoading}
				>
					跳过
				</Button>
			{/if}

			<Button type="submit" class="flex-1" disabled={$submitting || isLoading}>
				{#if $submitting || isLoading}
					<div
						class="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"
					></div>
					保存中...
				{:else}
					完成设置
				{/if}
			</Button>
		</div>
	</form>
</div>
