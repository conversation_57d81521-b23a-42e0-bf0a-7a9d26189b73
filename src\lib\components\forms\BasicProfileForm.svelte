<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { basicProfileSchema, type BasicProfile } from '$lib/schemas/profile';
	import {
		Page,
		Navbar,
		Block,
		BlockTitle,
		Button,
		List,
		ListInput,
		ListItem
	} from 'konsta/svelte';

	interface Props {
		data: any;
		onSubmit?: (data: BasicProfile) => void;
		onCancel?: () => void;
		isLoading?: boolean;
	}

	let { data, onSubmit, onCancel, isLoading = false }: Props = $props();

	const { form, errors, enhance, submitting } = superForm(data, {
		validators: zodClient(basicProfileSchema),
		onUpdated: ({ form }) => {
			if (form.valid && onSubmit) {
				onSubmit(form.data);
			}
		}
	});

	// Svelte 5 调试 - 监控表单状态
	$inspect('BasicProfileForm - 表单数据:', $form);
	$inspect('BasicProfileForm - 表单错误:', $errors);
	$inspect('BasicProfileForm - 提交状态:', $submitting);

	// 选项定义
	const orientationOptions = [
		{ value: 'straight', label: '异性恋' },
		{ value: 'gay', label: '男同性恋' },
		{ value: 'lesbian', label: '女同性恋' },
		{ value: 'bisexual', label: '双性恋' },
		{ value: 'asexual', label: '无性恋' },
		{ value: 'demisexual', label: '半性恋' },
		{ value: 'pansexual', label: '泛性恋' },
		{ value: 'queer', label: '酷儿' },
		{ value: 'fluid', label: '流动性恋' },
		{ value: 'other_orientation', label: '其他' },
		{ value: 'prefer_not_to_say_orientation', label: '不愿透露' }
	];

	const bodyTypeOptions = [
		{ value: 'male_body', label: '男性身体' },
		{ value: 'female_body', label: '女性身体' },
		{ value: 'other_body_type', label: '其他身体类型' }
	];

	const presentationStyleOptions = [
		{ value: 'conventional_masculine', label: '传统男性化' },
		{ value: 'rugged_masculine', label: '粗犷男性化' },
		{ value: 'feminine', label: '女性化' },
		{ value: 'androgynous_neutral', label: '中性化' },
		{ value: 'other_presentation_style', label: '其他风格' }
	];
</script>

<!-- 基础资料表单 -->
<Block class="text-center">
	<div class="mb-4 text-6xl">👤</div>
	<h2 class="mb-2 text-2xl font-bold">完善基础资料</h2>
	<p class="text-gray-600 dark:text-gray-300">这些信息将帮助我们为您提供更好的匹配体验</p>
</Block>

<form method="POST" use:enhance>
	<BlockTitle>基础信息</BlockTitle>
	<List strongIos insetIos>
		<ListInput
			label="昵称"
			type="text"
			placeholder="输入您的昵称"
			name="nickname"
			value={$form.nickname}
			disabled={$submitting || isLoading}
			error={$errors.nickname?.[0]}
			onInput={(e) => ($form.nickname = e.target.value)}
		/>

		<ListInput
			label="年龄"
			type="number"
			placeholder="18"
			min="18"
			max="99"
			name="age"
			value={$form.age}
			disabled={$submitting || isLoading}
			error={$errors.age?.[0]}
			onInput={(e) => ($form.age = parseInt(e.target.value) || '')}
		/>
	</List>

	<BlockTitle>个人偏好</BlockTitle>
	<List strongIos insetIos>
		<ListInput
			label="性取向"
			type="select"
			name="orientation"
			value={$form.orientation}
			disabled={$submitting || isLoading}
			error={$errors.orientation?.[0]}
			placeholder="请选择您的性取向"
			dropdown
			onChange={(e) => ($form.orientation = e.target.value)}
		>
			<option value="">请选择您的性取向</option>
			{#each orientationOptions as option}
				<option value={option.value}>{option.label}</option>
			{/each}
		</ListInput>

		<ListInput
			label="身体类型"
			type="select"
			name="bodyType"
			value={$form.bodyType}
			disabled={$submitting || isLoading}
			error={$errors.bodyType?.[0]}
			placeholder="请选择您的身体类型"
			dropdown
			onChange={(e) => ($form.bodyType = e.target.value)}
		>
			<option value="">请选择您的身体类型</option>
			{#each bodyTypeOptions as option}
				<option value={option.value}>{option.label}</option>
			{/each}
		</ListInput>
	</List>

	<BlockTitle>展现风格</BlockTitle>
	<List strongIos insetIos>
		<ListInput
			label="展现风格"
			type="select"
			name="presentationStyle"
			value={$form.presentationStyle}
			disabled={$submitting || isLoading}
			error={$errors.presentationStyle?.[0]}
			placeholder="请选择您的展现风格"
			dropdown
			onChange={(e) => ($form.presentationStyle = e.target.value)}
		>
			<option value="">请选择您的展现风格</option>
			{#each presentationStyleOptions as option}
				<option value={option.value}>{option.label}</option>
			{/each}
		</ListInput>
	</List>

	<!-- 隐私提醒 -->
	<Block class="bg-blue-50 dark:bg-blue-900/20">
		<div class="flex items-start space-x-2">
			<div class="text-lg text-blue-500">🛡️</div>
			<div>
				<h4 class="text-sm font-medium text-blue-900 dark:text-blue-100">隐私保护</h4>
				<p class="mt-1 text-xs text-blue-700 dark:text-blue-300">
					您的所有信息都是绝对隐私的，仅用于匹配算法，不会泄露给任何第三方。
				</p>
			</div>
		</div>
	</Block>

	<!-- 操作按钮 -->
	<Block class="space-y-4">
		{#if onCancel}
			<Button fill outline onclick={onCancel} disabled={$submitting || isLoading}>跳过</Button>
		{/if}

		<Button type="submit" fill disabled={$submitting || isLoading}>
			{#if $submitting || isLoading}
				保存中...
			{:else}
				完成设置
			{/if}
		</Button>
	</Block>
</form>
