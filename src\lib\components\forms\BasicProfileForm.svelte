<script lang="ts">
	import { superForm } from 'sveltekit-superforms';
	import { zodClient } from 'sveltekit-superforms/adapters';
	import { basicProfileSchema, type BasicProfile } from '$lib/schemas/profile';
	import {
		Page,
		Navbar,
		Block,
		BlockTitle,
		Button,
		List,
		ListInput,
		ListItem
	} from 'konsta/svelte';

	interface Props {
		data: any;
		onSubmit?: (data: BasicProfile) => void;
		onCancel?: () => void;
		isLoading?: boolean;
	}

	let { data, onSubmit, onCancel, isLoading = false }: Props = $props();

	const { form, errors, enhance, submitting } = superForm(data, {
		validators: zodClient(basicProfileSchema),
		onUpdated: ({ form }) => {
			if (form.valid && onSubmit) {
				onSubmit(form.data);
			}
		}
	});

	// Svelte 5 调试 - 监控表单状态
	$inspect('BasicProfileForm - 表单数据:', $form);
	$inspect('BasicProfileForm - 表单错误:', $errors);
	$inspect('BasicProfileForm - 提交状态:', $submitting);

	// 身体类型选项
	const bodyTypeOptions = [
		{ value: 'male_body', label: '男性身体' },
		{ value: 'female_body', label: '女性身体' },
		{ value: 'other_body_type', label: '其他身体类型' }
	];
</script>

<!-- 基础资料表单 -->
<Block class="text-center">
	<div class="mb-4 text-6xl">👤</div>
	<h2 class="mb-2 text-2xl font-bold">完善基础资料</h2>
	<p class="text-gray-600 dark:text-gray-300">这些基础信息是必填的，用于基础匹配</p>
</Block>

<form method="POST" use:enhance>
	<BlockTitle>基础信息</BlockTitle>
	<List strongIos insetIos>
		<ListInput
			label="昵称"
			type="text"
			placeholder="输入您的昵称"
			name="nickname"
			value={$form.nickname}
			disabled={$submitting || isLoading}
			error={$errors.nickname && $errors.nickname[0]}
			onInput={(e) => ($form.nickname = e.target.value)}
		/>

		<ListInput
			label="年龄"
			type="number"
			placeholder="18"
			min="18"
			max="99"
			name="age"
			value={$form.age}
			disabled={$submitting || isLoading}
			error={$errors.age && $errors.age[0]}
			onInput={(e) => ($form.age = parseInt(e.target.value) || '')}
		/>

		<ListInput
			label="身高 (cm)"
			type="number"
			placeholder="170"
			min="100"
			max="250"
			name="heightCm"
			value={$form.heightCm}
			disabled={$submitting || isLoading}
			error={$errors.heightCm && $errors.heightCm[0]}
			onInput={(e) => ($form.heightCm = parseInt(e.target.value) || '')}
		/>

		<ListInput
			label="体重 (kg)"
			type="number"
			placeholder="65"
			min="30"
			max="300"
			name="weightKg"
			value={$form.weightKg}
			disabled={$submitting || isLoading}
			error={$errors.weightKg && $errors.weightKg[0]}
			onInput={(e) => ($form.weightKg = parseInt(e.target.value) || '')}
		/>
	</List>

	<BlockTitle>地理位置</BlockTitle>
	<List strongIos insetIos>
		<ListInput
			label="国家"
			type="text"
			placeholder="中国"
			name="country"
			value={$form.country}
			disabled={$submitting || isLoading}
			error={$errors.country && $errors.country[0]}
			onInput={(e) => ($form.country = e.target.value)}
		/>

		<ListInput
			label="城市"
			type="text"
			placeholder="北京"
			name="city"
			value={$form.city}
			disabled={$submitting || isLoading}
			error={$errors.city && $errors.city[0]}
			onInput={(e) => ($form.city = e.target.value)}
		/>
	</List>

	<BlockTitle>身体类型</BlockTitle>
	<List strongIos insetIos>
		<ListInput
			label="身体类型"
			type="select"
			name="bodyType"
			value={$form.bodyType}
			disabled={$submitting || isLoading}
			error={$errors.bodyType && $errors.bodyType[0]}
			placeholder="请选择您的身体类型"
			dropdown
			onChange={(e) => ($form.bodyType = e.target.value)}
		>
			<option value="">请选择您的身体类型</option>
			{#each bodyTypeOptions as option}
				<option value={option.value}>{option.label}</option>
			{/each}
		</ListInput>
	</List>

	<BlockTitle>个人简介 (可选)</BlockTitle>
	<List strongIos insetIos>
		<ListInput
			label="个人简介"
			type="textarea"
			placeholder="简单介绍一下自己..."
			name="bio"
			value={$form.bio}
			disabled={$submitting || isLoading}
			error={$errors.bio && $errors.bio[0]}
			onInput={(e) => ($form.bio = e.target.value)}
		/>
	</List>

	<!-- 隐私提醒 -->
	<Block class="bg-blue-50 dark:bg-blue-900/20">
		<div class="flex items-start space-x-2">
			<div class="text-lg text-blue-500">🛡️</div>
			<div>
				<h4 class="text-sm font-medium text-blue-900 dark:text-blue-100">基础资料说明</h4>
				<p class="mt-1 text-xs text-blue-700 dark:text-blue-300">
					基础资料用于基础匹配。完成后可选择填写高级资料以使用高级搜索功能。
				</p>
			</div>
		</div>
	</Block>

	<!-- 操作按钮 -->
	<Block class="space-y-4">
		{#if onCancel}
			<Button class="w-full" outline onclick={onCancel} disabled={$submitting || isLoading}>
				跳过
			</Button>
		{/if}

		<Button type="submit" class="w-full" disabled={$submitting || isLoading}>
			{#if $submitting || isLoading}
				保存中...
			{:else}
				完成基础设置
			{/if}
		</Button>
	</Block>
</form>
