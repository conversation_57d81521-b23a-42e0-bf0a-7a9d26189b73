import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import type { PageServerLoad } from './$types';

export const load: PageServerLoad = async () => {
	try {
		console.log('🔍 测试数据库连接...');
		
		// 尝试查询用户表
		const userCount = await db.select().from(users).limit(1);
		console.log('✅ 数据库连接成功，用户数量:', userCount.length);
		
		return {
			success: true,
			message: '数据库连接成功',
			userCount: userCount.length,
			users: userCount
		};
	} catch (error) {
		console.error('❌ 数据库连接失败:', error);
		
		return {
			success: false,
			message: '数据库连接失败',
			error: error.message || String(error)
		};
	}
};
