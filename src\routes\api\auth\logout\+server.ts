import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import * as auth from '$lib/server/auth';

export const POST: RequestHandler = async (event) => {
	const { locals } = event;

	if (!locals.session) {
		return json({ success: false, message: '没有活跃的会话' }, { status: 400 });
	}

	try {
		// 使用标准认证系统使session无效
		await auth.invalidateSession(locals.session.id);

		// 删除session cookie
		auth.deleteSessionTokenCookie(event);

		return json({ success: true, message: '登出成功' });
	} catch (error) {
		console.error('登出失败:', error);
		return json({ success: false, message: '登出失败' }, { status: 500 });
	}
};
