<script lang="ts">
	import { goto } from '$app/navigation';
	import { formatPoints, formatVipLevel } from '$lib/utils/formatting';
	import { decodeRoles } from '$lib/utils/kinkUtils';
	import { KINK_ROLE_LABELS } from '$lib/constants/kink';
	import { telegramWebApp } from '$lib/telegram';
	import {
		Page,
		Navbar,
		Block,
		BlockTitle,
		Button,
		List,
		ListItem,
		Badge,
		Progressbar
	} from 'konsta/svelte';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	// 使用server-side数据
	let user = $derived(data.user);
	let session = $derived(data.session);

	// 计算资料完整度
	let completeness = $derived(() => {
		if (!user) return 0;
		let score = 20; // 基础分数
		if (user.age) score += 10;
		if (user.bio) score += 10;
		if (user.orientation) score += 15;
		if (user.bodyType) score += 15;
		if (user.presentationStyle) score += 15;
		if (user.country) score += 5;
		if (user.city) score += 5;
		if (user.kinkCategoryBitmask && user.kinkCategoryBitmask > 0) score += 15;
		return Math.min(score, 100);
	});

	// 计算用户的kink角色
	let userKinkRoles = $derived(() => {
		if (user?.kinkCategoryBitmask) {
			return decodeRoles(user.kinkCategoryBitmask);
		}
		return [];
	});

	// Svelte 5 调试 - 监控profile页面状态
	$inspect('Profile页面 - 用户资料:', user);
	$inspect('Profile页面 - 完成度:', completeness);
	$inspect('Profile页面 - Kink角色:', userKinkRoles);

	function handleEditProfile() {
		goto('/onboarding/basic?edit=true');
	}

	function handleEditAdvanced() {
		goto('/onboarding/advanced?edit=true');
	}

	function handleShareKinkMap() {
		if (user?.kinkMapCode) {
			telegramWebApp.shareKinkMap(user.kinkMapCode);
		}
	}

	function handleInviteFriend() {
		if (user?.kinkMapCode) {
			telegramWebApp.inviteFriend(user.kinkMapCode);
		}
	}

	function handleSettings() {
		goto('/settings');
	}

	async function handleLogout() {
		try {
			const response = await fetch('/api/auth/logout', {
				method: 'POST'
			});

			if (response.ok) {
				goto('/');
			}
		} catch (error) {
			console.error('登出失败:', error);
		}
	}
</script>

<svelte:head>
	<title>我的资料 - BlueX</title>
</svelte:head>

<Page>
	<Navbar title="我的资料">
		<Button slot="right" clear onclick={handleSettings}>设置</Button>
	</Navbar>

	{#if !user}
		<Block class="py-20 text-center">
			<div class="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"></div>
			<p class="text-gray-600 dark:text-gray-300">加载中...</p>
		</Block>
	{:else}
		<!-- 用户基本信息 -->
		<Block class="text-center">
			<div class="mb-4">
				{#if user.profileImageUrl}
					<img
						src={user.profileImageUrl}
						alt={user.nickname}
						class="mx-auto h-24 w-24 rounded-full object-cover"
					/>
				{:else}
					<div
						class="mx-auto flex h-24 w-24 items-center justify-center rounded-full bg-gray-300 dark:bg-gray-600"
					>
						<span class="text-2xl">👤</span>
					</div>
				{/if}
			</div>

			<h2 class="mb-2 text-xl font-bold">
				{user.nickname}
				{#if user.isVerified}
					<Badge class="ml-2">已验证</Badge>
				{/if}
			</h2>

			<div class="mb-2 text-sm text-gray-600 dark:text-gray-300">
				{#if user.age}
					{user.age}岁
				{/if}
				{#if user.city || user.country}
					• {user.city || user.country}
				{/if}
			</div>

			<div class="mb-4 flex justify-center space-x-2">
				<Badge color="blue">{formatVipLevel(user.vipLevel)}</Badge>
				<Badge color="green">信誉分 {user.trustScore}</Badge>
			</div>

			{#if user.bio}
				<p class="text-sm text-gray-700 dark:text-gray-300">
					{user.bio}
				</p>
			{/if}
		</Block>

		<!-- 资料完整度 -->
		<BlockTitle>资料完整度 ({completeness()}%)</BlockTitle>
		<Block>
			<Progressbar progress={completeness()} />
		</Block>

		<!-- Kink 偏好信息 -->
		{#if userKinkRoles().length > 0}
			<BlockTitle>我的偏好</BlockTitle>
			<List strongIos insetIos>
				<ListItem title="角色偏好">
					<div slot="after" class="flex flex-wrap gap-1">
						{#each userKinkRoles().slice(0, 3) as role}
							<Badge color="purple" class="text-xs">
								{KINK_ROLE_LABELS[role]}
							</Badge>
						{/each}
						{#if userKinkRoles().length > 3}
							<Badge color="gray" class="text-xs">
								+{userKinkRoles().length - 3}
							</Badge>
						{/if}
					</div>
				</ListItem>
			</List>
		{/if}

		<!-- 积分信息 -->
		<BlockTitle>我的积分</BlockTitle>
		<Block class="rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 text-white">
			<div class="flex items-center justify-between">
				<div>
					<h3 class="mb-1 text-lg font-semibold">积分余额</h3>
					<p class="text-2xl font-bold">{formatPoints(user.pointBalance)}</p>
				</div>
				<div class="text-4xl">⭐</div>
			</div>
		</Block>

		<!-- 操作菜单 -->
		<List strongIos insetIos>
			<ListItem title="编辑基础资料" link onclick={handleEditProfile}>
				<span slot="media">✏️</span>
			</ListItem>

			<ListItem title="编辑高级设置" link onclick={handleEditAdvanced}>
				<span slot="media">⚙️</span>
			</ListItem>

			<ListItem title="分享我的 Kink Map" link onclick={handleShareKinkMap}>
				<span slot="media">🔗</span>
			</ListItem>

			<ListItem title="邀请朋友" link onclick={handleInviteFriend}>
				<span slot="media">👥</span>
			</ListItem>
		</List>

		<!-- 退出登录 -->
		<Block>
			<Button fill color="red" onclick={handleLogout}>退出登录</Button>
		</Block>
	{/if}
</Page>
