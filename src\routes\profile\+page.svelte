<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import {
		isAuthenticated,
		userProfile,
		profileCompleteness,
		userActions,
		currentUser
	} from '$lib/stores';
	import { formatPoints, formatVipLevel } from '$lib/utils/formatting';
	import { decodeRoles, formatRoles } from '$lib/utils/kinkUtils';
	import { KINK_ROLE_LABELS } from '$lib/constants/kink';
	import { telegramWebApp } from '$lib/telegram';
	import {
		Page,
		Navbar,
		Block,
		BlockTitle,
		Button,
		List,
		ListItem,
		Badge,
		Progressbar
	} from 'konsta/svelte';

	let user = $derived($userProfile);
	let completeness = $derived($profileCompleteness);
	let authUser = $derived($currentUser);

	// 计算用户的kink角色
	let userKinkRoles = $derived(() => {
		if (user?.kinkCategoryBitmask) {
			return decodeRoles(user.kinkCategoryBitmask);
		}
		return [];
	});

	// Svelte 5 调试 - 监控profile页面状态
	$inspect('Profile页面 - 认证状态:', $isAuthenticated);
	$inspect('Profile页面 - 用户资料:', user);
	$inspect('Profile页面 - 认证用户:', authUser);
	$inspect('Profile页面 - 完成度:', completeness);
	$inspect('Profile页面 - Kink角色:', userKinkRoles);

	onMount(async () => {
		console.log('🔍 Profile页面初始化');

		// 如果已认证但没有profile，尝试加载
		if ($isAuthenticated && !user && authUser) {
			console.log('🔄 尝试加载用户profile...');
			try {
				await userActions.loadProfileByTelegramId(authUser.id);
				console.log('✅ Profile加载完成');
			} catch (error) {
				console.error('❌ Profile加载失败:', error);
			}
		}
	});

	function handleEditProfile() {
		goto('/onboarding/basic?edit=true');
	}

	function handleEditAdvanced() {
		goto('/onboarding/advanced?edit=true');
	}

	function handleShareKinkMap() {
		if (user?.kinkMapCode) {
			telegramWebApp.shareKinkMap(user.kinkMapCode);
		}
	}

	function handleInviteFriend() {
		if (user?.kinkMapCode) {
			telegramWebApp.inviteFriend(user.kinkMapCode);
		}
	}

	function handleSettings() {
		goto('/settings');
	}

	function handleLogout() {
		// 登出逻辑
		userActions.logout();
		goto('/');
	}
</script>

<svelte:head>
	<title>我的资料 - BlueX</title>
</svelte:head>

<Page>
	<Navbar title="我的资料">
		<Button slot="right" clear onclick={handleSettings}>设置</Button>
	</Navbar>

	{#if !$isAuthenticated}
		<Block class="py-20 text-center">
			<div class="mb-4 text-6xl">🔐</div>
			<h2 class="mb-2 text-xl font-semibold">请先登录</h2>
			<p class="text-gray-600 dark:text-gray-300">需要登录后才能查看个人资料</p>
		</Block>
	{:else if !user}
		<Block class="py-20 text-center">
			<div class="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"></div>
			<p class="text-gray-600 dark:text-gray-300">加载中...</p>
		</Block>
	{:else}
		<!-- 用户基本信息 -->
		<Block class="text-center">
			<div class="mb-4">
				{#if user.profileImageUrl}
					<img
						src={user.profileImageUrl}
						alt={user.nickname}
						class="mx-auto h-24 w-24 rounded-full object-cover"
					/>
				{:else}
					<div
						class="mx-auto flex h-24 w-24 items-center justify-center rounded-full bg-gray-300 dark:bg-gray-600"
					>
						<span class="text-2xl">👤</span>
					</div>
				{/if}
			</div>

			<h2 class="mb-2 text-xl font-bold">
				{user.nickname}
				{#if user.isVerified}
					<Badge class="ml-2">已验证</Badge>
				{/if}
			</h2>

			<div class="mb-2 text-sm text-gray-600 dark:text-gray-300">
				{#if user.age}
					{user.age}岁
				{/if}
				{#if user.city || user.country}
					• {user.city || user.country}
				{/if}
			</div>

			<div class="mb-4 flex justify-center space-x-2">
				<Badge color="blue">{formatVipLevel(user.vipLevel)}</Badge>
				<Badge color="green">信誉分 {user.trustScore}</Badge>
			</div>

			{#if user.bio}
				<p class="text-sm text-gray-700 dark:text-gray-300">
					{user.bio}
				</p>
			{/if}
		</Block>

		<!-- 资料完整度 -->
		<BlockTitle>资料完整度 ({completeness}%)</BlockTitle>
		<Block>
			<Progressbar progress={completeness} />
		</Block>

		<!-- Kink 偏好信息 -->
		{#if userKinkRoles.length > 0}
			<BlockTitle>我的偏好</BlockTitle>
			<List strongIos insetIos>
				<ListItem title="角色偏好">
					<div slot="after" class="flex flex-wrap gap-1">
						{#each userKinkRoles.slice(0, 3) as role}
							<Badge color="purple" class="text-xs">
								{KINK_ROLE_LABELS[role]}
							</Badge>
						{/each}
						{#if userKinkRoles.length > 3}
							<Badge color="gray" class="text-xs">
								+{userKinkRoles.length - 3}
							</Badge>
						{/if}
					</div>
				</ListItem>
			</List>
		{/if}

		<!-- 积分信息 -->
		<BlockTitle>我的积分</BlockTitle>
		<Block class="rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 text-white">
			<div class="flex items-center justify-between">
				<div>
					<h3 class="mb-1 text-lg font-semibold">积分余额</h3>
					<p class="text-2xl font-bold">{formatPoints(user.pointBalance)}</p>
				</div>
				<div class="text-4xl">⭐</div>
			</div>
		</Block>

		<!-- 操作菜单 -->
		<List strongIos insetIos>
			<ListItem title="编辑基础资料" link onclick={handleEditProfile}>
				<span slot="media">✏️</span>
			</ListItem>

			<ListItem title="编辑高级设置" link onclick={handleEditAdvanced}>
				<span slot="media">⚙️</span>
			</ListItem>

			<ListItem title="分享我的 Kink Map" link onclick={handleShareKinkMap}>
				<span slot="media">🔗</span>
			</ListItem>

			<ListItem title="邀请朋友" link onclick={handleInviteFriend}>
				<span slot="media">👥</span>
			</ListItem>
		</List>

		<!-- 退出登录 -->
		<Block>
			<Button fill color="red" onclick={handleLogout}>退出登录</Button>
		</Block>
	{/if}
</Page>
