<script lang="ts">
	import { onMount } from 'svelte';
	import {
		isAuthenticated,
		userProfile,
		profileCompleteness,
		userActions,
		currentUser
	} from '$lib/stores';
	import { formatPoints, formatVipLevel } from '$lib/utils/formatting';
	import { telegramWebApp } from '$lib/telegram';
	import { Button } from '$lib/components/ui/button';
	import {
		User,
		Settings,
		Share2,
		Star,
		Shield,
		LogOut,
		Edit,
		Camera,
		MapPin,
		Calendar
	} from '@lucide/svelte';

	let user = $derived($userProfile);
	let completeness = $derived($profileCompleteness);
	let authUser = $derived($currentUser);

	// Svelte 5 调试 - 监控profile页面状态
	$inspect('Profile页面 - 认证状态:', $isAuthenticated);
	$inspect('Profile页面 - 用户资料:', user);
	$inspect('Profile页面 - 认证用户:', authUser);
	$inspect('Profile页面 - 完成度:', completeness);

	onMount(async () => {
		console.log('🔍 Profile页面初始化');

		// 如果已认证但没有profile，尝试加载
		if ($isAuthenticated && !user && authUser) {
			console.log('🔄 尝试加载用户profile...');
			try {
				await userActions.loadProfileByTelegramId(authUser.id);
				console.log('✅ Profile加载完成');
			} catch (error) {
				console.error('❌ Profile加载失败:', error);
			}
		}
	});

	function handleEditProfile() {
		// 跳转到编辑资料页面
		console.log('Edit profile');
	}

	function handleShareKinkMap() {
		if (user?.kinkMapCode) {
			telegramWebApp.shareKinkMap(user.kinkMapCode);
		}
	}

	function handleInviteFriend() {
		if (user?.kinkMapCode) {
			telegramWebApp.inviteFriend(user.kinkMapCode);
		}
	}

	function handleSettings() {
		// 跳转到设置页面
		console.log('Settings');
	}

	function handleLogout() {
		// 登出逻辑
		console.log('Logout');
	}
</script>

<svelte:head>
	<title>我的资料 - BlueX</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
	{#if !$isAuthenticated}
		<div class="flex min-h-screen items-center justify-center">
			<div class="text-center">
				<div class="mb-4 text-4xl">🔐</div>
				<h2 class="mb-2 text-xl font-semibold text-gray-900 dark:text-white">请先登录</h2>
				<p class="text-gray-600 dark:text-gray-300">需要登录后才能查看个人资料</p>
			</div>
		</div>
	{:else if !user}
		<div class="flex min-h-screen items-center justify-center">
			<div class="text-center">
				<div
					class="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2 border-blue-500"
				></div>
				<p class="text-gray-600 dark:text-gray-300">加载中...</p>
			</div>
		</div>
	{:else}
		<!-- 页面头部 -->
		<header
			class="border-b border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
		>
			<div class="px-4 py-4">
				<div class="flex items-center justify-between">
					<h1 class="text-xl font-bold text-gray-900 dark:text-white">我的资料</h1>
					<Button variant="ghost" size="icon" onclick={handleSettings}>
						<Settings class="h-5 w-5" />
					</Button>
				</div>
			</div>
		</header>

		<main class="space-y-6 p-4">
			<!-- 用户信息卡片 -->
			<div
				class="overflow-hidden rounded-lg border border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
			>
				<!-- 头像和基本信息 -->
				<div class="p-6">
					<div class="flex items-center space-x-4">
						<div class="relative">
							{#if user.profileImageUrl}
								<img
									src={user.profileImageUrl}
									alt={user.nickname}
									class="h-20 w-20 rounded-full object-cover"
								/>
							{:else}
								<div
									class="flex h-20 w-20 items-center justify-center rounded-full bg-gray-300 dark:bg-gray-600"
								>
									<User class="h-10 w-10 text-gray-600 dark:text-gray-300" />
								</div>
							{/if}
							<button
								class="absolute -right-1 -bottom-1 flex h-6 w-6 items-center justify-center rounded-full bg-blue-500 text-white"
							>
								<Camera class="h-3 w-3" />
							</button>
						</div>

						<div class="flex-1">
							<div class="flex items-center space-x-2">
								<h2 class="text-xl font-bold text-gray-900 dark:text-white">
									{user.nickname}
								</h2>
								{#if user.isVerified}
									<Shield class="h-5 w-5 text-blue-500" />
								{/if}
							</div>

							<div
								class="mt-2 flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-300"
							>
								{#if user.age}
									<div class="flex items-center space-x-1">
										<Calendar class="h-4 w-4" />
										<span>{user.age}岁</span>
									</div>
								{/if}
								{#if user.city || user.country}
									<div class="flex items-center space-x-1">
										<MapPin class="h-4 w-4" />
										<span>{user.city || user.country}</span>
									</div>
								{/if}
							</div>

							<div class="mt-2 flex items-center space-x-2">
								<span
									class="rounded-full bg-blue-100 px-2 py-1 text-xs text-blue-700 dark:bg-blue-900/20 dark:text-blue-300"
								>
									{formatVipLevel(user.vipLevel)}
								</span>
								<span
									class="rounded-full bg-green-100 px-2 py-1 text-xs text-green-700 dark:bg-green-900/20 dark:text-green-300"
								>
									信誉分 {user.trustScore}
								</span>
							</div>
						</div>
					</div>

					{#if user.bio}
						<p class="mt-4 text-gray-700 dark:text-gray-300">
							{user.bio}
						</p>
					{/if}
				</div>

				<!-- 资料完整度 -->
				<div
					class="border-t border-gray-200 bg-gray-50 px-6 py-4 dark:border-gray-600 dark:bg-gray-700/50"
				>
					<div class="mb-2 flex items-center justify-between">
						<span class="text-sm font-medium text-gray-700 dark:text-gray-300"> 资料完整度 </span>
						<span class="text-sm font-bold text-blue-600 dark:text-blue-400">
							{completeness}%
						</span>
					</div>
					<div class="h-2 w-full rounded-full bg-gray-200 dark:bg-gray-600">
						<div
							class="h-2 rounded-full bg-blue-500 transition-all duration-300"
							style="width: {completeness}%"
						></div>
					</div>
				</div>

				<!-- 操作按钮 -->
				<div class="border-t border-gray-200 p-4 dark:border-gray-600">
					<Button onclick={handleEditProfile} class="mb-3 w-full">
						<Edit class="mr-2 h-4 w-4" />
						编辑资料
					</Button>
				</div>
			</div>

			<!-- 积分和等级 -->
			<div class="rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 p-6 text-white">
				<div class="flex items-center justify-between">
					<div>
						<h3 class="mb-1 text-lg font-semibold">我的积分</h3>
						<p class="text-2xl font-bold">{formatPoints(user.pointBalance)}</p>
					</div>
					<Star class="h-8 w-8 text-yellow-300" />
				</div>
			</div>

			<!-- 功能菜单 -->
			<div class="space-y-3">
				<Button variant="outline" onclick={handleShareKinkMap} class="w-full justify-start">
					<Share2 class="mr-3 h-4 w-4" />
					分享我的 Kink Map
				</Button>

				<Button variant="outline" onclick={handleInviteFriend} class="w-full justify-start">
					<User class="mr-3 h-4 w-4" />
					邀请朋友
				</Button>

				<Button
					variant="outline"
					onclick={handleLogout}
					class="w-full justify-start border-red-200 text-red-600 hover:bg-red-50"
				>
					<LogOut class="mr-3 h-4 w-4" />
					退出登录
				</Button>
			</div>
		</main>
	{/if}
</div>
