// Application Constants

export const APP_CONFIG = {
	name: 'BlueX',
	version: '1.0.0',
	telegram: {
		botUsername: 'sveltekit_bot' // 替换为你的实际 bot username
	},
	points: {
		registration_bonus: 100,
		daily_check_in: 10,
		profile_completion_bonus: 50,
		search_cost: 5,
		super_search_cost: 20,
		super_like_cost: 10,
		invite_bonus: 30
	},
	search: {
		basic_daily_limit: 50,
		advanced_daily_limit: 20,
		super_daily_limit: 5,
		results_per_page: 20
	},
	profile: {
		min_age: 18,
		max_age: 99,
		min_nickname_length: 2,
		max_nickname_length: 20,
		max_bio_length: 500
	}
} as const;

export const TABS: Array<{
	id: string;
	label: string;
	icon: string;
	path: string;
	requiresAuth: boolean;
	requiresBasicProfile: boolean;
}> = [
	{
		id: 'discover',
		label: '发现',
		icon: '🔍',
		path: '/discover',
		requiresAuth: true,
		requiresBasicProfile: true
	},
	{
		id: 'interactions',
		label: '互动',
		icon: '💝',
		path: '/interactions',
		requiresAuth: true,
		requiresBasicProfile: true
	},
	{
		id: 'growth',
		label: '成长',
		icon: '🌟',
		path: '/growth',
		requiresAuth: true,
		requiresBasicProfile: false
	},
	{
		id: 'profile',
		label: '我的',
		icon: '👤',
		path: '/profile',
		requiresAuth: true,
		requiresBasicProfile: false
	}
];

export const KINK_CATEGORIES = {
	// todo
} as const;

export const ORIENTATIONS = [
	{ value: 'straight', label: '异性恋' },
	{ value: 'gay', label: '男同性恋' },
	{ value: 'lesbian', label: '女同性恋' },
	{ value: 'bisexual', label: '双性恋' },
	{ value: 'asexual', label: '无性恋' },
	{ value: 'demisexual', label: '半性恋' },
	{ value: 'pansexual', label: '泛性恋' },
	{ value: 'queer', label: '酷儿' },
	{ value: 'fluid', label: '流动性' },
	{ value: 'other_orientation', label: '其他' },
	{ value: 'prefer_not_to_say_orientation', label: '不愿透露' }
] as const;

export const BODY_TYPES = [
	{ value: 'male_body', label: '男性身体' },
	{ value: 'female_body', label: '女性身体' },
	{ value: 'other_body_type', label: '其他身体类型' }
] as const;

export const PRESENTATION_STYLES = [
	{ value: 'conventional_masculine', label: '传统男性化' },
	{ value: 'rugged_masculine', label: '粗犷男性化' },
	{ value: 'feminine', label: '女性化' },
	{ value: 'androgynous_neutral', label: '中性/雌雄同体' },
	{ value: 'other_presentation_style', label: '其他风格' }
] as const;

export const RELATIONSHIP_STATUSES = [
	{ value: 'single', label: '单身' },
	{ value: 'in_a_relationship', label: '恋爱中' },
	{ value: 'complicated', label: '复杂关系' },
	{ value: 'open_relationship', label: '开放关系' },
	{ value: 'married', label: '已婚' },
	{ value: 'polyamorous', label: '多元恋爱' },
	{ value: 'other_relationship_status', label: '其他' },
	{ value: 'prefer_not_to_say_relationship_status', label: '不愿透露' }
] as const;
