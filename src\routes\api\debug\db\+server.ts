import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';

export const GET: RequestHandler = async () => {
	try {
		console.log('🔍 API测试数据库连接...');
		
		// 尝试查询用户表
		const userList = await db.select().from(users).limit(3);
		console.log('✅ 数据库连接成功，用户数量:', userList.length);
		
		return json({
			success: true,
			message: '数据库连接成功',
			userCount: userList.length,
			users: userList,
			timestamp: new Date().toISOString()
		});
	} catch (error) {
		console.error('❌ 数据库连接失败:', error);
		
		return json({
			success: false,
			message: '数据库连接失败',
			error: error.message || String(error),
			timestamp: new Date().toISOString()
		}, { status: 500 });
	}
};
