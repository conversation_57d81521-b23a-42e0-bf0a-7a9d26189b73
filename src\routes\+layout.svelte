<script lang="ts">
	import '../app.css';
	import { App } from 'konsta/svelte';
	import TabNavigation from '$lib/components/TabNavigation.svelte';
	import Modal from '$lib/components/Modal.svelte';
	import Toast from '$lib/components/Toast.svelte';
	import LoadingOverlay from '$lib/components/LoadingOverlay.svelte';
	import LoginPrompt from '$lib/components/LoginPrompt.svelte';

	let { children, data }: { children: any; data: any } = $props();

	// 使用server-side认证数据
	let user = $derived(data?.user);
	let isAuthenticated = $derived(!!user);

	// Svelte 5 调试
	$inspect('Layout.svelte - data:', data);
	$inspect('Layout.svelte - user:', user);
	$inspect('Layout.svelte - isAuthenticated:', isAuthenticated);
</script>

<App theme="ios">
	<div class="app-container min-h-screen bg-gray-50 dark:bg-gray-900">
		{#if isAuthenticated || data?.skipAuth}
			<main class="pb-16">
				{@render children()}
			</main>
			{#if isAuthenticated}
				<TabNavigation />
			{/if}
		{:else}
			<LoginPrompt />
		{/if}

		<!-- Global UI Components -->
		<Modal />
		<Toast />
		<LoadingOverlay />
	</div>
</App>
