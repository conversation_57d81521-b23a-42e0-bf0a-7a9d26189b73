import { z } from 'zod';
import { KINK_ROLES, type KinkRole, type KinkCategory } from '$lib/constants/kink';

// 基础用户资料 Schema
export const basicProfileSchema = z.object({
	nickname: z
		.string()
		.min(2, '昵称至少需要 2 个字符')
		.max(20, '昵称不能超过 20 个字符')
		.regex(/^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/, '昵称只能包含字母、数字、中文、下划线和连字符'),
	
	age: z
		.number({ coerce: true })
		.int('年龄必须是整数')
		.min(18, '年龄必须大于等于 18 岁')
		.max(99, '年龄不能超过 99 岁'),
	
	orientation: z.enum([
		'straight',
		'gay', 
		'lesbian',
		'bisexual',
		'asexual',
		'demisexual',
		'pansexual',
		'queer',
		'fluid',
		'other_orientation',
		'prefer_not_to_say_orientation'
	], {
		errorMap: () => ({ message: '请选择有效的性取向' })
	}),
	
	bodyType: z.enum([
		'male_body',
		'female_body', 
		'other_body_type'
	], {
		errorMap: () => ({ message: '请选择有效的身体类型' })
	}),
	
	presentationStyle: z.enum([
		'conventional_masculine',
		'rugged_masculine',
		'feminine',
		'androgynous_neutral',
		'other_presentation_style'
	], {
		errorMap: () => ({ message: '请选择有效的展示风格' })
	}),
	
	bio: z
		.string()
		.max(500, '个人简介不能超过 500 个字符')
		.optional(),
	
	country: z
		.string()
		.min(1, '请输入国家')
		.max(50, '国家名称不能超过 50 个字符')
		.optional(),
	
	city: z
		.string()
		.max(50, '城市名称不能超过 50 个字符')
		.optional()
});

// 高级用户资料 Schema
export const advancedProfileSchema = z.object({
	heightCm: z
		.number({ coerce: true })
		.int('身高必须是整数')
		.min(100, '身高不能小于 100cm')
		.max(250, '身高不能超过 250cm')
		.optional(),
	
	weightKg: z
		.number({ coerce: true })
		.int('体重必须是整数')
		.min(30, '体重不能小于 30kg')
		.max(300, '体重不能超过 300kg')
		.optional(),
	
	relationshipStatus: z.enum([
		'single',
		'in_a_relationship',
		'complicated',
		'open_relationship',
		'married',
		'polyamorous',
		'other_relationship_status',
		'prefer_not_to_say_relationship_status'
	]).optional(),
	
	// Kink 相关字段
	kinkRoles: z
		.array(z.enum(Object.keys(KINK_ROLES) as [KinkRole, ...KinkRole[]]))
		.max(8, '最多只能选择 8 个角色')
		.optional(),
	
	kinkRatings: z
		.record(z.string(), z.number().min(-1).max(5))
		.optional(),
	
	// 隐私设置
	blockVisibilityFromBodyTypes: z
		.array(z.enum(['male_body', 'female_body', 'other_body_type']))
		.optional(),
	
	blockVisibilityFromPresentationStyles: z
		.array(z.enum([
			'conventional_masculine',
			'rugged_masculine',
			'feminine',
			'androgynous_neutral',
			'other_presentation_style'
		]))
		.optional(),
	
	blockVisibilityFromOrientations: z
		.array(z.enum([
			'straight',
			'gay', 
			'lesbian',
			'bisexual',
			'asexual',
			'demisexual',
			'pansexual',
			'queer',
			'fluid',
			'other_orientation',
			'prefer_not_to_say_orientation'
		]))
		.optional()
});

// 完整的用户资料 Schema
export const fullProfileSchema = basicProfileSchema.merge(advancedProfileSchema);

// Kink 偏好设置 Schema
export const kinkPreferencesSchema = z.object({
	roles: z
		.array(z.enum(Object.keys(KINK_ROLES) as [KinkRole, ...KinkRole[]]))
		.min(1, '请至少选择一个角色')
		.max(8, '最多只能选择 8 个角色'),
	
	ratings: z
		.record(z.string(), z.number().min(-1).max(5))
		.refine(
			(ratings) => Object.keys(ratings).length > 0,
			{ message: '请至少对一个类别进行评分' }
		),
	
	// 是否公开显示 kink 信息
	isPublic: z.boolean().default(false),
	
	// 匹配偏好
	preferredCompatibilityLevel: z
		.number()
		.min(0)
		.max(1)
		.default(0.5), // 0.5 表示中等兼容性要求
});

// 隐私设置 Schema
export const privacySettingsSchema = z.object({
	// 谁可以看到我的资料
	profileVisibility: z.enum(['public', 'matches_only', 'private']).default('public'),
	
	// 谁可以给我发消息
	messagePermissions: z.enum(['everyone', 'matches_only', 'none']).default('matches_only'),
	
	// 是否显示在线状态
	showOnlineStatus: z.boolean().default(true),
	
	// 是否显示最后活跃时间
	showLastActive: z.boolean().default(true),
	
	// 屏蔽设置
	blockSettings: z.object({
		bodyTypes: z.array(z.enum(['male_body', 'female_body', 'other_body_type'])).default([]),
		presentationStyles: z.array(z.enum([
			'conventional_masculine',
			'rugged_masculine',
			'feminine',
			'androgynous_neutral',
			'other_presentation_style'
		])).default([]),
		orientations: z.array(z.enum([
			'straight',
			'gay', 
			'lesbian',
			'bisexual',
			'asexual',
			'demisexual',
			'pansexual',
			'queer',
			'fluid',
			'other_orientation',
			'prefer_not_to_say_orientation'
		])).default([]),
		ageRange: z.object({
			min: z.number().min(18).max(99).default(18),
			max: z.number().min(18).max(99).default(99)
		}).refine(
			(data) => data.min <= data.max,
			{ message: '最小年龄不能大于最大年龄' }
		).optional()
	}).default({
		bodyTypes: [],
		presentationStyles: [],
		orientations: []
	})
});

// 用户注册 Schema
export const userRegistrationSchema = z.object({
	telegramUserId: z.number().int().positive(),
	telegramUsername: z.string().optional(),
	referrerCode: z.string().optional()
});

// 搜索过滤器 Schema
export const searchFiltersSchema = z.object({
	ageRange: z.object({
		min: z.number().min(18).max(99).default(18),
		max: z.number().min(18).max(99).default(99)
	}).refine(
		(data) => data.min <= data.max,
		{ message: '最小年龄不能大于最大年龄' }
	).optional(),
	
	orientations: z.array(z.enum([
		'straight', 'gay', 'lesbian', 'bisexual', 'asexual', 'demisexual',
		'pansexual', 'queer', 'fluid', 'other_orientation', 'prefer_not_to_say_orientation'
	])).optional(),
	
	bodyTypes: z.array(z.enum(['male_body', 'female_body', 'other_body_type'])).optional(),
	
	presentationStyles: z.array(z.enum([
		'conventional_masculine', 'rugged_masculine', 'feminine',
		'androgynous_neutral', 'other_presentation_style'
	])).optional(),
	
	relationshipStatuses: z.array(z.enum([
		'single', 'in_a_relationship', 'complicated', 'open_relationship',
		'married', 'polyamorous', 'other_relationship_status', 'prefer_not_to_say_relationship_status'
	])).optional(),
	
	location: z.object({
		country: z.string().optional(),
		city: z.string().optional(),
		radius: z.number().min(1).max(1000).optional() // km
	}).optional(),
	
	kinkCompatibility: z.object({
		requiredRoles: z.array(z.enum(Object.keys(KINK_ROLES) as [KinkRole, ...KinkRole[]])).optional(),
		minCompatibilityScore: z.number().min(0).max(1).default(0.3)
	}).optional(),
	
	// 排序选项
	sortBy: z.enum(['last_active', 'trust_score', 'compatibility', 'distance']).default('last_active'),
	sortOrder: z.enum(['asc', 'desc']).default('desc')
});

// 类型导出
export type BasicProfile = z.infer<typeof basicProfileSchema>;
export type AdvancedProfile = z.infer<typeof advancedProfileSchema>;
export type FullProfile = z.infer<typeof fullProfileSchema>;
export type KinkPreferences = z.infer<typeof kinkPreferencesSchema>;
export type PrivacySettings = z.infer<typeof privacySettingsSchema>;
export type UserRegistration = z.infer<typeof userRegistrationSchema>;
export type SearchFilters = z.infer<typeof searchFiltersSchema>;
