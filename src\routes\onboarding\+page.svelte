<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/stores';
	import { Button } from '$lib/components/ui/button';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import { Progress } from '$lib/components/ui/progress';
	import { 
		User, 
		Heart, 
		Settings, 
		CheckCircle, 
		Circle, 
		ArrowRight,
		Star,
		Shield
	} from '@lucide/svelte';

	// 从URL参数或store中获取用户信息
	let currentStep = $derived($page.url.searchParams.get('step') || 'welcome');
	let userProfile = $state({
		hasBasicProfile: false,
		hasAdvancedProfile: false,
		profileCompletenessScore: 20
	});

	// 步骤定义
	const steps = [
		{
			id: 'basic',
			title: '基础资料',
			description: '填写基本个人信息',
			icon: User,
			required: true,
			completed: userProfile.hasBasicProfile,
			path: '/onboarding/basic',
			estimatedTime: '2-3分钟'
		},
		{
			id: 'advanced',
			title: '高级资料',
			description: '设置偏好和隐私选项',
			icon: Heart,
			required: false,
			completed: userProfile.hasAdvancedProfile,
			path: '/onboarding/advanced',
			estimatedTime: '3-5分钟'
		},
		{
			id: 'privacy',
			title: '隐私设置',
			description: '配置谁可以看到您的信息',
			icon: Shield,
			required: false,
			completed: false,
			path: '/onboarding/privacy',
			estimatedTime: '1-2分钟'
		}
	];

	// 计算完成进度
	let completedSteps = $derived(steps.filter(step => step.completed).length);
	let totalSteps = $derived(steps.length);
	let progressPercentage = $derived((completedSteps / totalSteps) * 100);

	function navigateToStep(stepPath: string) {
		goto(stepPath);
	}

	function skipToDiscover() {
		goto('/discover?onboarding_skipped=true');
	}

	function getStepStatus(step: typeof steps[0]) {
		if (step.completed) return 'completed';
		if (step.required && !step.completed) return 'required';
		return 'optional';
	}

	function getStepIcon(step: typeof steps[0]) {
		if (step.completed) return CheckCircle;
		return step.icon;
	}

	function getStepBadgeVariant(step: typeof steps[0]) {
		const status = getStepStatus(step);
		switch (status) {
			case 'completed': return 'default';
			case 'required': return 'destructive';
			case 'optional': return 'secondary';
			default: return 'secondary';
		}
	}

	function getStepBadgeText(step: typeof steps[0]) {
		const status = getStepStatus(step);
		switch (status) {
			case 'completed': return '已完成';
			case 'required': return '必填';
			case 'optional': return '可选';
			default: return '';
		}
	}
</script>

<svelte:head>
	<title>完善资料 - BlueX</title>
	<meta name="description" content="完善您的个人资料，获得更好的匹配体验" />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 dark:from-gray-900 dark:to-gray-800 p-4">
	<div class="max-w-2xl mx-auto space-y-6">
		<!-- 欢迎标题 -->
		<div class="text-center space-y-4">
			<div class="flex items-center justify-center">
				<div class="p-3 bg-primary/10 rounded-full">
					<Star class="h-8 w-8 text-primary" />
				</div>
			</div>
			<div>
				<h1 class="text-3xl font-bold text-gray-900 dark:text-white">
					欢迎来到 BlueX
				</h1>
				<p class="text-lg text-gray-600 dark:text-gray-300 mt-2">
					让我们帮您完善资料，开始精彩的发现之旅
				</p>
			</div>
		</div>

		<!-- 进度条 -->
		<Card>
			<CardHeader>
				<div class="flex items-center justify-between">
					<CardTitle class="text-lg">完成进度</CardTitle>
					<Badge variant="outline">{completedSteps}/{totalSteps}</Badge>
				</div>
				<Progress value={progressPercentage} class="w-full" />
			</CardHeader>
			<CardContent>
				<p class="text-sm text-muted-foreground">
					资料完整度：{userProfile.profileCompletenessScore}%
					{#if userProfile.profileCompletenessScore < 60}
						- 完善资料可以获得更多匹配机会
					{:else if userProfile.profileCompletenessScore < 90}
						- 您的资料已经很不错了！
					{:else}
						- 完美！您的资料非常完整
					{/if}
				</p>
			</CardContent>
		</Card>

		<!-- 步骤列表 -->
		<div class="space-y-4">
			{#each steps as step}
				<Card class="transition-all hover:shadow-md cursor-pointer" onclick={() => navigateToStep(step.path)}>
					<CardHeader>
						<div class="flex items-center justify-between">
							<div class="flex items-center space-x-3">
								<div class="p-2 rounded-full {step.completed ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'}">
									<svelte:component this={getStepIcon(step)} class="h-5 w-5" />
								</div>
								<div>
									<CardTitle class="text-lg flex items-center gap-2">
										{step.title}
										<Badge variant={getStepBadgeVariant(step)} class="text-xs">
											{getStepBadgeText(step)}
										</Badge>
									</CardTitle>
									<CardDescription>
										{step.description} • 预计 {step.estimatedTime}
									</CardDescription>
								</div>
							</div>
							<ArrowRight class="h-5 w-5 text-gray-400" />
						</div>
					</CardHeader>
				</Card>
			{/each}
		</div>

		<!-- 操作按钮 -->
		<div class="space-y-3">
			{#if completedSteps === 0}
				<!-- 还没开始填写 -->
				<Button 
					size="lg" 
					class="w-full" 
					onclick={() => navigateToStep('/onboarding/basic')}
				>
					开始填写资料
				</Button>
				<Button 
					variant="outline" 
					size="lg" 
					class="w-full" 
					onclick={skipToDiscover}
				>
					暂时跳过，直接探索
				</Button>
			{:else if steps.find(s => s.required && !s.completed)}
				<!-- 还有必填项未完成 -->
				<Button 
					size="lg" 
					class="w-full" 
					onclick={() => {
						const nextRequired = steps.find(s => s.required && !s.completed);
						if (nextRequired) navigateToStep(nextRequired.path);
					}}
				>
					继续完善必填信息
				</Button>
				<Button 
					variant="outline" 
					size="lg" 
					class="w-full" 
					onclick={() => goto('/discover')}
				>
					进入发现页面
				</Button>
			{:else}
				<!-- 必填项已完成 -->
				<Button 
					size="lg" 
					class="w-full" 
					onclick={() => goto('/discover')}
				>
					开始探索匹配
				</Button>
				{#if completedSteps < totalSteps}
					<Button 
						variant="outline" 
						size="lg" 
						class="w-full" 
						onclick={() => {
							const nextOptional = steps.find(s => !s.completed);
							if (nextOptional) navigateToStep(nextOptional.path);
						}}
					>
						完善更多信息
					</Button>
				{/if}
			{/if}
		</div>

		<!-- 帮助信息 -->
		<Card class="bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
			<CardContent class="pt-6">
				<div class="flex items-start space-x-3">
					<div class="p-1 bg-blue-100 dark:bg-blue-900 rounded-full">
						<Settings class="h-4 w-4 text-blue-600 dark:text-blue-400" />
					</div>
					<div class="space-y-1">
						<p class="text-sm font-medium text-blue-900 dark:text-blue-100">
							为什么要完善资料？
						</p>
						<ul class="text-xs text-blue-700 dark:text-blue-300 space-y-1">
							<li>• 获得更精准的匹配推荐</li>
							<li>• 提高其他用户对您的信任度</li>
							<li>• 解锁更多高级搜索功能</li>
							<li>• 获得积分奖励和特殊徽章</li>
						</ul>
					</div>
				</div>
			</CardContent>
		</Card>
	</div>
</div>
