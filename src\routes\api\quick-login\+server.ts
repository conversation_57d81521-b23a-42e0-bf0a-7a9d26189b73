import { json, redirect } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import * as auth from '$lib/server/auth';

export const POST: RequestHandler = async (event) => {
	try {
		console.log('🔍 快速登录测试...');
		
		// 查找测试用户
		const testUser = await db.query.users.findFirst({
			where: eq(users.telegramUserId, 123456789)
		});

		if (!testUser) {
			return json({ success: false, message: '测试用户不存在' }, { status: 404 });
		}

		console.log('✅ 找到测试用户:', testUser.nickname);

		// 创建session
		const sessionToken = auth.generateSessionToken();
		const session = await auth.createSession(sessionToken, testUser.id);
		console.log('🔑 创建会话成功:', session.id);

		// 设置cookie
		auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);

		return json({
			success: true,
			message: '登录成功',
			user: {
				id: testUser.id,
				nickname: testUser.nickname,
				telegramUserId: testUser.telegramUserId
			},
			sessionToken
		});
	} catch (error) {
		console.error('❌ 快速登录失败:', error);
		
		return json({
			success: false,
			message: '登录失败',
			error: error.message || String(error)
		}, { status: 500 });
	}
};
