import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import * as auth from '$lib/server/auth';

export const GET: RequestHandler = async (event) => {
	try {
		// 获取session token
		const sessionToken = event.cookies.get(auth.sessionCookieName);
		
		if (!sessionToken) {
			return json({
				success: false,
				message: '没有session token',
				cookieName: auth.sessionCookieName
			});
		}

		// 验证session
		const { session, user } = await auth.validateSession(sessionToken);
		
		return json({
			success: true,
			sessionToken,
			hasSession: !!session,
			hasUser: !!user,
			session: session ? {
				id: session.id,
				userId: session.userId,
				expiresAt: session.expiresAt
			} : null,
			user: user ? {
				id: user.id,
				nickname: user.nickname,
				telegramUserId: user.telegramUserId
			} : null,
			timestamp: new Date().toISOString()
		});
	} catch (error) {
		return json({
			success: false,
			error: error.message || String(error),
			timestamp: new Date().toISOString()
		}, { status: 500 });
	}
};
