import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';

export const GET: RequestHandler = async ({ locals }) => {
	return json({
		authenticated: !!locals.user,
		user: locals.user ? {
			id: locals.user.id,
			nickname: locals.user.nickname,
			telegramUserId: locals.user.telegramUserId
		} : null,
		session: locals.session ? {
			id: locals.session.id,
			expiresAt: locals.session.expiresAt
		} : null
	});
};
