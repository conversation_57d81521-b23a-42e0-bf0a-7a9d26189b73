<script lang="ts">
	import { 
		Block, 
		BlockTitle,
		Button,
		List,
		ListInput,
		ListItem
	} from 'konsta/svelte';

	interface Props {
		searchType: 'basic' | 'advanced';
		onClose: () => void;
	}

	let { searchType, onClose }: Props = $props();

	// 筛选状态
	let ageRange = $state({ min: 18, max: 99 });
	let selectedBodyTypes = $state([]);
	let selectedCountries = $state([]);

	function applyFilters() {
		console.log('应用筛选条件:', {
			ageRange,
			selectedBodyTypes,
			selectedCountries
		});
		// TODO: 实现筛选逻辑
		onClose();
	}

	function resetFilters() {
		ageRange = { min: 18, max: 99 };
		selectedBodyTypes = [];
		selectedCountries = [];
	}
</script>

<Block class="bg-white dark:bg-gray-800 border-t">
	<BlockTitle>搜索筛选</BlockTitle>
	
	<!-- 年龄范围 -->
	<List strongIos insetIos>
		<ListItem>
			<div class="w-full">
				<div class="flex items-center justify-between mb-2">
					<span class="text-sm font-medium">年龄范围</span>
					<span class="text-sm text-gray-500">{ageRange.min} - {ageRange.max}岁</span>
				</div>
				<div class="flex space-x-4">
					<ListInput
						type="number"
						placeholder="最小"
						min="18"
						max="99"
						value={ageRange.min}
						onInput={(e) => ageRange.min = parseInt(e.target.value) || 18}
					/>
					<ListInput
						type="number"
						placeholder="最大"
						min="18"
						max="99"
						value={ageRange.max}
						onInput={(e) => ageRange.max = parseInt(e.target.value) || 99}
					/>
				</div>
			</div>
		</ListItem>
	</List>

	<!-- 基础筛选 -->
	<BlockTitle>基础筛选</BlockTitle>
	<List strongIos insetIos>
		<ListInput
			label="地区"
			type="text"
			placeholder="输入国家或城市"
			value=""
			onInput={(e) => console.log('地区筛选:', e.target.value)}
		/>
	</List>

	<!-- 高级筛选 (仅在高级搜索时显示) -->
	{#if searchType === 'advanced'}
		<BlockTitle>高级筛选</BlockTitle>
		<List strongIos insetIos>
			<ListItem>
				<span class="text-sm text-gray-600">高级筛选功能开发中...</span>
			</ListItem>
		</List>
	{/if}

	<!-- 操作按钮 -->
	<Block class="space-y-4">
		<div class="flex space-x-4">
			<Button 
				class="flex-1"
				outline
				onclick={resetFilters}
			>
				重置
			</Button>
			<Button 
				class="flex-1"
				onclick={applyFilters}
			>
				应用筛选
			</Button>
		</div>
		<Button 
			class="w-full"
			outline
			onclick={onClose}
		>
			关闭
		</Button>
	</Block>
</Block>
