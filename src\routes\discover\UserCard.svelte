<script lang="ts">
	import { <PERSON><PERSON>, Card } from 'konsta/svelte';

	interface User {
		id: string;
		nickname: string;
		age: number;
		heightCm: number;
		weightKg: number;
		country: string;
		city: string;
		bodyType: string;
		bio?: string;
		profileImageUrl?: string;
		hasAvatar: boolean;
		trustScore: number;
		lastActiveAt: string;
		// 高级字段
		orientation?: string;
		presentationStyle?: string;
		relationshipStatus?: string;
		hasAdvancedProfile: boolean;
	}

	interface Props {
		user: User;
		searchType: 'basic' | 'advanced';
		onLike: () => void;
		onPass: () => void;
	}

	let { user, searchType, onLike, onPass }: Props = $props();

	// 身体类型标签
	const bodyTypeLabels = {
		male_body: '男性身体',
		female_body: '女性身体',
		other_body_type: '其他身体类型'
	};

	// 计算年龄显示
	const ageDisplay = $derived(`${user.age}岁`);

	// 计算身高体重显示
	const physicalDisplay = $derived(`${user.heightCm}cm / ${user.weightKg}kg`);

	// 计算位置显示
	const locationDisplay = $derived(`${user.city}, ${user.country}`);
</script>

<Card class="mb-4">
	<!-- 用户头像和基础信息 -->
	<div class="flex items-start space-x-4 p-4">
		<!-- 头像 -->
		<div class="flex-shrink-0">
			{#if user.hasAvatar && user.profileImageUrl}
				<img
					src={user.profileImageUrl}
					alt={user.nickname}
					class="h-16 w-16 rounded-full object-cover"
				/>
			{:else}
				<div
					class="flex h-16 w-16 items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700"
				>
					<span class="text-2xl">👤</span>
				</div>
			{/if}
		</div>

		<!-- 基础信息 -->
		<div class="min-w-0 flex-1">
			<div class="mb-1 flex items-center justify-between">
				<h3 class="truncate text-lg font-semibold">{user.nickname}</h3>
				<div class="flex items-center space-x-1">
					<span class="text-sm text-gray-500">信誉</span>
					<span class="text-sm font-medium">{user.trustScore}</span>
				</div>
			</div>

			<div class="space-y-1 text-sm text-gray-600 dark:text-gray-300">
				<div>{ageDisplay} • {physicalDisplay}</div>
				<div>{bodyTypeLabels[user.bodyType]}</div>
				<div>📍 {locationDisplay}</div>
			</div>
		</div>
	</div>

	<!-- 个人简介 -->
	{#if user.bio}
		<div class="px-4 pb-2">
			<h4 class="mb-1 text-sm font-medium">个人简介</h4>
			<p class="text-sm text-gray-600 dark:text-gray-300">{user.bio}</p>
		</div>
	{/if}

	<!-- 操作按钮 -->
	<div class="flex space-x-4 p-4 pt-2">
		<Button class="flex-1" outline onclick={onPass}>👎 跳过</Button>
		<Button class="flex-1" onclick={onLike}>👍 喜欢</Button>
	</div>
</Card>
