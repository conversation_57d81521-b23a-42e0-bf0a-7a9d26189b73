# TMA 项目 "BlueX" 开发总纲

**版本:** 1.0
**最后更新:** 2025-06-24

## 1. 项目概述

本项目是一个基于 Telegram Mini App (TMA) 的社交应用，旨在为特定兴趣社群提供一个安全、私密、体验流畅的交流和发现平台。核心特点包括精准的用户画像、高级的匹配筛选、以及未来计划集成的 TON 钱包和代币经济系统。

## 2. 核心技术栈

- **框架:** SvelteKit
- **UI:** Konsta UI + Tailwind CSS
- **鉴权:** Lucia Auth (基于 TMA `initData` 的无密码模式)
- **数据库:** PostgreSQL
- **ORM:** Drizzle ORM
- **表单处理:** SvelteKit Superforms + Zod
- **国际化:** ParaglideJS
- **图标:** Lucide Svelte

## 3. 核心架构原则

- **唯一信任根源:** 认证流程完全依赖 Telegram 提供的 `initData`，并通过后端使用 `BOT_TOKEN` 进行严格的加密校验。无传统的密码登录体系。
- **数据库驱动的会话:** 使用 Lucia 实现安全的、服务端的会话管理，会话信息存储在 PostgreSQL 数据库中，客户端仅持有无意义的 `HttpOnly` Session Cookie。
- **渐进式用户引导:** 用户通过 TMA 首次访问时即自动创建基础账户。通过应用内的激励和功能解锁，引导用户逐步完善个人资料。
- **组件驱动开发:** 优先构建可复用的UI组件，再组装成页面。
- **全栈类型安全:** 利用 SvelteKit, Drizzle, Zod, Superforms, Lucia 实现从数据库到前端的端到端类型安全。

## 4. 开发里程碑 (任务链)

这是一个循环渐进的开发计划。请按顺序完成每个里程碑中的任务。每个里程碑都有其专属的详细指南文档。

- [ ] **[里程碑 1: 奠定基石 - 数据库与认证系统](./MILESTONE-1-FOUNDATION.md)**
  - `1.1` 项目初始化与环境配置
  - `1.2` 数据库与 Drizzle Schema 终版设计
  - `1.3` Lucia Auth 核心服务搭建
  - `1.4` 实现 TMA 登录/注册一体化 API
  - `1.5` 实现用户登出功能

- [ ] **[里程碑 2: 核心用户体验 - 资料与发现](./MILESTONE-2-CORE-UX.md)**
  - `2.1` 实现渐进式用户资料填写流程 (Onboarding)
  - `2.2` 实现个人资料查看与编辑页面 (Profile)
  - `2.3` 实现基础的用户发现与筛选页面 (Discover)

- [ ] **[里程碑 3: 社交与分享功能](./MILESTONE-3-SOCIAL.md)**
  - `3.1` 实现“喜欢”与“匹配”的交互逻辑
  * `3.2` 实现“互动”页面 (收到的喜欢、我的匹配)
  * `3.3` 实现 `kink_map_code` 的分享页面

- [ ] **[里程碑 4: 成长与商业化系统](./MILESTONE-4-GROWTH.md)**
  - `4.1` 实现积分系统与任务列表
  - `4.2` 实现高级/VIP搜索功能
- [ ] **[里程碑 5: Web3 集成](./MILESTONE-5-WEB3.md)**
  - `5.1` 集成 TON Connect UI 实现钱包连接
  - `5.2` 实现钱包地址与用户账户的绑定
  * `5.3` 代币经济系统初步设计

## 5. 如何使用本开发指南 (对 AI Copilot 的指示)

1.  **开始任务前:** 首先阅读 `PROJECT_README.md` 确认总体目标，然后打开 `PROGRESS_LOG.md` 查看当前项目状态和下一个待办任务。
2.  **执行任务时:** 打开对应里程碑的详细指南文件 (如 `MILESTONE-1-FOUNDATION.md`)，严格按照其中的任务描述、代码示例和最佳实践来编写代码。
3.  **完成任务后:** 回到 `PROGRESS_LOG.md`，更新已完成任务的状态，并简要记录遇到的问题或关键决策。然后标记下一个任务为“进行中”。
4.  **若会话中断/重置:** 通过快速阅读 `PROGRESS_LOG.md`，你可以立刻恢复上下文，知道我们进行到了哪一步。
5.  **调试命令:** 通过 `pnpm run dev --host --open` 启动开发进程,同时请使用playrightMCP调试 chrome浏览器的 `https://web.telegram.org/a/#93372553` 下的 tma页面
6.  **请勿随便生成新的调试用的文件**
