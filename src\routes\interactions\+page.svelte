<script lang="ts">
	import { onMount } from 'svelte';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	let isAuthenticated = data?.user !== null;

	let activeTab = $state<'likes' | 'matches' | 'blocked'>('likes');
	let likes = $state([]);
	let matches = $state([]);
	let blocked = $state([]);

	onMount(() => {
		// 加载互动数据
		loadInteractions();
	});

	async function loadInteractions() {
		// 这里会调用 API 加载数据
		// likes = await fetchLikes();
		// matches = await fetchMatches();
		// blocked = await fetchBlocked();
	}

	function setActiveTab(tab: 'likes' | 'matches' | 'blocked') {
		activeTab = tab;
	}
</script>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
	{#if !isAuthenticated}
		<div class="flex min-h-screen items-center justify-center">
			<div class="text-center">
				<div class="mb-4 text-4xl">🔐</div>
				<h2 class="mb-2 text-xl font-semibold text-gray-900 dark:text-white">请先登录</h2>
				<p class="text-gray-600 dark:text-gray-300">需要登录后才能查看互动记录</p>
			</div>
		</div>
	{:else}
		<!-- 页面头部 -->
		<header
			class="border-b border-gray-200 bg-white shadow-sm dark:border-gray-700 dark:bg-gray-800"
		>
			<div class="px-4 py-4">
				<h1 class="text-xl font-bold text-gray-900 dark:text-white">互动</h1>
				<p class="mt-1 text-sm text-gray-600 dark:text-gray-300">管理您的喜欢、匹配和屏蔽</p>
			</div>

			<!-- Tab 导航 -->
			<div class="flex border-b border-gray-200 dark:border-gray-700">
				<button
					onclick={() => setActiveTab('likes')}
					class="flex-1 px-4 py-3 text-center text-sm font-medium transition-colors {activeTab ===
					'likes'
						? 'border-b-2 border-blue-600 text-blue-600 dark:border-blue-400 dark:text-blue-400'
						: 'text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white'}"
				>
					收到的喜欢
				</button>
				<button
					onclick={() => setActiveTab('matches')}
					class="flex-1 px-4 py-3 text-center text-sm font-medium transition-colors {activeTab ===
					'matches'
						? 'border-b-2 border-blue-600 text-blue-600 dark:border-blue-400 dark:text-blue-400'
						: 'text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white'}"
				>
					匹配
				</button>
				<button
					onclick={() => setActiveTab('blocked')}
					class="flex-1 px-4 py-3 text-center text-sm font-medium transition-colors {activeTab ===
					'blocked'
						? 'border-b-2 border-blue-600 text-blue-600 dark:border-blue-400 dark:text-blue-400'
						: 'text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white'}"
				>
					已屏蔽
				</button>
			</div>
		</header>

		<!-- 内容区域 -->
		<main class="p-4">
			{#if activeTab === 'likes'}
				<div class="space-y-4">
					<h2 class="text-lg font-semibold text-gray-900 dark:text-white">收到的喜欢</h2>
					{#if likes.length === 0}
						<div class="py-12 text-center">
							<div class="mb-4 text-4xl">💝</div>
							<h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">还没有收到喜欢</h3>
							<p class="text-gray-600 dark:text-gray-300">完善您的资料，增加被发现的机会</p>
						</div>
					{:else}
						<!-- 喜欢列表 -->
						<div class="space-y-3">
							{#each likes as like}
								<!-- 喜欢卡片组件 -->
							{/each}
						</div>
					{/if}
				</div>
			{:else if activeTab === 'matches'}
				<div class="space-y-4">
					<h2 class="text-lg font-semibold text-gray-900 dark:text-white">匹配</h2>
					{#if matches.length === 0}
						<div class="py-12 text-center">
							<div class="mb-4 text-4xl">💫</div>
							<h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">还没有匹配</h3>
							<p class="text-gray-600 dark:text-gray-300">去发现页面寻找志同道合的朋友</p>
						</div>
					{:else}
						<!-- 匹配列表 -->
						<div class="space-y-3">
							{#each matches as match}
								<!-- 匹配卡片组件 -->
							{/each}
						</div>
					{/if}
				</div>
			{:else if activeTab === 'blocked'}
				<div class="space-y-4">
					<h2 class="text-lg font-semibold text-gray-900 dark:text-white">已屏蔽</h2>
					{#if blocked.length === 0}
						<div class="py-12 text-center">
							<div class="mb-4 text-4xl">🚫</div>
							<h3 class="mb-2 text-lg font-medium text-gray-900 dark:text-white">
								没有屏蔽任何用户
							</h3>
							<p class="text-gray-600 dark:text-gray-300">屏蔽的用户将不会出现在搜索结果中</p>
						</div>
					{:else}
						<!-- 屏蔽列表 -->
						<div class="space-y-3">
							{#each blocked as blockedUser}
								<!-- 屏蔽用户卡片组件 -->
							{/each}
						</div>
					{/if}
				</div>
			{/if}
		</main>
	{/if}
</div>
