import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import * as auth from '$lib/server/auth';

export const GET: RequestHandler = async (event) => {
	const allCookies = {};
	
	// 获取所有cookies
	event.cookies.getAll().forEach(cookie => {
		allCookies[cookie.name] = cookie.value;
	});

	// 检查特定的session cookie
	const sessionToken = event.cookies.get(auth.sessionCookieName);
	
	return json({
		cookieName: auth.sessionCookieName,
		sessionToken: sessionToken || null,
		allCookies,
		timestamp: new Date().toISOString()
	});
};
