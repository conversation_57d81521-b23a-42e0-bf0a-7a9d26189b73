import * as auth from '$lib/server/auth'; // 导入您在别处初始化的 Lucia 实例
import type { LayoutServerLoad } from './$types';

// 这个 load 函数会在每一次服务器端渲染或导航时运行
export const load: LayoutServerLoad = async (event) => {
	// 1. 从 event.cookies 中获取浏览器发送过来的 session cookie
	const sessionId = event.cookies.get(auth.sessionCookieName);

	// 2. 如果没有 session cookie，说明用户肯定没登录
	if (!sessionId) {
		// 将 user 和 session 设为 null，并返回
		// 前端布局可以根据 user 是否为 null 来决定显示登录页还是主应用界面
		return {
			user: null,
			session: null
		};
	}

	// 3. 如果有 session cookie，调用 lucia.validateSession() 来验证它
	// 这个函数会去数据库的 sessions 表里查找，并检查是否过期
	const { session, user } = await auth.validateSession(sessionId);

	// 4. 如果 session 过期或无效，清除浏览器中无效的 cookie
	if (!session) {
		const sessionCookie = auth.createBlankSessionCookie();
		event.cookies.set(sessionCookie.name, sessionCookie.value, sessionCookie.attributes);
	}

	// 5. 如果 session 有效，Lucia 会同时返回 session 和 user 的信息

	// 6. 最终，将 user 和 session 对象返回。
	// 这样，在您的根布局 `+layout.svelte` 中，就可以通过 `export let data;`
	// 来接收到 data.user 和 data.session，从而在全局控制用户的登录状态显示。
	return {
		user: user,
		session: session
	};
};
