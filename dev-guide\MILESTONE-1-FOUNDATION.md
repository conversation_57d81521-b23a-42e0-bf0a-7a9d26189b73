# 里程碑 1 指南：奠定基石 - 数据库与认证系统

**目标:** 完成项目的初始化，搭建好数据库结构，并实现一个安全、完整的、基于 TMA `initData` 的用户认证流程（包括登录和登出）。

---

### 任务 1.1: 项目初始化与环境配置

1.  **初始化 SvelteKit 项目:**
    ```bash
    pnpm create svelte@latest blueX
    ```
    (选择 "Skeleton project" -> "TypeScript" -> "ESLint" -> "Prettier" -> "Playwright" -> "Vitest")

2.  **安装核心依赖:** 进入项目目录，执行以下命令安装我们之前讨论过的所有依赖。
    ```bash
    # 生产依赖
    pnpm install zod drizzle-orm pg @tma.js/sdk-svelte lucia @lucia-auth/adapter-drizzle oslo sveltekit-superforms nanoid konsta @konstaui/svelte tailwindcss postcss autoprefixer clsx tailwind-merge
    
    # 开发依赖
    pnpm install -D drizzle-kit @types/pg
    ```

3.  **初始化 Tailwind CSS 和 Konsta UI:** 按照它们各自官网的 SvelteKit 指南进行配置。

4.  **配置 `.env` 文件:** 在项目根目录创建 `.env` 文件，并填入您的 `BOT_TOKEN` 和数据库连接字符串。
    ```env
    # .env
    DATABASE_URL="postgresql://postgres:your_strong_password@localhost:30701/kink_tma_db"
    BOT_TOKEN="12345:ABCDEFG..."
    ```

5.  **配置 `app.html`:** 在 `src/app.html` 的 `<head>` 中加入 Telegram SDK 脚本。
    ```html
    <script src="[https://telegram.org/js/telegram-web-app.js](https://telegram.org/js/telegram-web-app.js)"></script>
    ```

### 任务 1.2: 数据库与 Drizzle Schema 终版设计

1.  **目标:** 将我们最终确定的数据库结构，用 Drizzle ORM 的语法在 `src/lib/server/db/schema.ts` 文件中实现。
2.  **核心代码:** （此处省略完整的 schema 代码，AI 可根据我们之前的讨论生成，或从之前的会话中复制最终版本）。
3.  **操作:**
    * 编写 `schema.ts` 文件，包含 `users`, `sessions`, `keys`, `matches`, `point_transactions` 五张表。
    * 确保所有 `id` 和外键都使用了 `text()` 类型。
    * 运行 `pnpm drizzle-kit generate:pg` 生成 SQL 迁移文件。
    * 运行 `pnpm drizzle-kit push:pg` (开发阶段) 或您选择的迁移命令，将 schema 应用到数据库。

### 任务 1.3: Lucia Auth 核心服务搭建

1.  **目标:** 初始化 Lucia 并设置全局会话验证钩子。
2.  **创建 `src/lib/server/auth.ts`:**
    ```typescript
    import { Lucia } from 'lucia';
    import { DrizzlePostgreSQLAdapter } from '@lucia-auth/adapter-drizzle';
    import { db } from '$lib/server/db'; // 假设 Drizzle Client 在这里
    import { sessions, users, keys } from '$lib/server/db/schema';

    const adapter = new DrizzlePostgreSQLAdapter(db, sessions, users); // 旧版 keys 表现在由 adapter 内部管理

    export const lucia = new Lucia(adapter, {
        sessionCookie: {
            attributes: {
                secure: process.env.NODE_ENV === 'production'
            }
        },
        getUserAttributes: (attributes) => {
            return {
                telegramUserId: attributes.telegramUserId,
                username: attributes.telegramUsername,
                nickname: attributes.nickname
            };
        }
    });

    declare module 'lucia' {
        interface Register {
            Lucia: typeof lucia;
            DatabaseUserAttributes: {
                telegramUserId: number;
                username: string | null;
                nickname: string;
            };
        }
    }
    ```
3.  **创建 `src/hooks.server.ts`:**
    ```typescript
    import { lucia } from '$lib/server/auth';
    import type { Handle } from '@sveltejs/kit';

    export const handle: Handle = async ({ event, resolve }) => {
        const sessionId = event.cookies.get(lucia.sessionCookieName);
        if (!sessionId) {
            event.locals.user = null;
            event.locals.session = null;
            return resolve(event);
        }

        const { session, user } = await lucia.validateSession(sessionId);
        if (session && session.fresh) {
            const sessionCookie = lucia.createSessionCookie(session.id);
            event.cookies.set(sessionCookie.name, sessionCookie.value, {
                path: '.',
                ...sessionCookie.attributes
            });
        }
        if (!session) {
            const sessionCookie = lucia.createBlankSessionCookie();
            event.cookies.set(sessionCookie.name, sessionCookie.value, {
                path: '.',
                ...sessionCookie.attributes
            });
        }
        event.locals.user = user;
        event.locals.session = session;
        return resolve(event);
    };
    ```

### 任务 1.4: 实现 TMA 登录/注册一体化 API

1.  **目标:** 创建一个端点，接收前端发来的 `initData`，完成验证、用户创建/查找、以及会话创建。
2.  **创建 `src/routes/api/auth/telegram/+server.ts`:**
    ```typescript
    // 此处省略完整代码，AI 可从我们之前的讨论中获取
    // 核心逻辑：
    // 1. 从 request body 获取 initData
    // 2. 使用 BOT_TOKEN 导入并 validate(initData, BOT_TOKEN)
    // 3. 解析出 telegramUser
    // 4. db.query.users.findFirst(...) 查找用户
    // 5. 如果用户不存在：
    //    a. 使用 nanoid 生成 userId 和 kinkMapCode
    //    b. db.insert(users).values(...) 创建用户
    //    c. db.insert(keys).values({ id: `telegram:${telegramUser.id}`, userId: userId }) 创建 key
    // 6. 调用 lucia.createSession(userId, {})
    // 7. 调用 lucia.createSessionCookie(session.id)
    // 8. 用 cookies.set() 设置 cookie
    // 9. 返回 json({ success: true })
    ```

### 任务 1.5: 实现用户登出功能

1.  **目标:** 创建一个让用户能安全登出的 Action 或 API 端点。
2.  **创建 `src/routes/logout/+server.ts`:**
    ```typescript
    import { lucia } from '$lib/server/auth';
    import { fail, redirect } from '@sveltejs/kit';
    import type { RequestHandler } from './$types';

    export const POST: RequestHandler = async ({ locals, cookies }) => {
        if (!locals.session) {
            return fail(401);
        }
        await lucia.invalidateSession(locals.session.id);
        const sessionCookie = lucia.createBlankSessionCookie();
        cookies.set(sessionCookie.name, sessionCookie.value, {
            path: '.',
            ...sessionCookie.attributes
        });
        throw redirect(303, '/'); // 重定向到主页
    };
    ```

---