// Kink-related utility functions

import { KINK_ROLES, COMPATIBLE_ROLES, type KinkRole, type KinkRating } from '$lib/constants/kink';

/**
 * 将选中的角色数组编码为位掩码
 * @param selectedRoles 选中的角色数组
 * @returns 编码后的位掩码数字
 */
export function encodeRoles(selectedRoles: KinkRole[]): number {
	return selectedRoles.reduce((bitmask, role) => bitmask | KINK_ROLES[role], 0);
}

/**
 * 将位掩码解码为角色数组
 * @param bitmask 位掩码数字
 * @returns 解码后的角色数组
 */
export function decodeRoles(bitmask: number): KinkRole[] {
	const roles: KinkRole[] = [];
	
	// 遍历所有可能的角色
	for (const [roleKey, roleValue] of Object.entries(KINK_ROLES)) {
		if ((bitmask & roleValue) > 0) {
			roles.push(roleKey as KinkRole);
		}
	}
	
	return roles;
}

/**
 * 检查用户是否拥有特定角色
 * @param userBitmask 用户的角色位掩码
 * @param role 要检查的角色
 * @returns 是否拥有该角色
 */
export function hasRole(userBitmask: number, role: KinkRole): boolean {
	return (userBitmask & KINK_ROLES[role]) > 0;
}

/**
 * 添加角色到现有的位掩码
 * @param currentBitmask 当前的位掩码
 * @param role 要添加的角色
 * @returns 新的位掩码
 */
export function addRole(currentBitmask: number, role: KinkRole): number {
	return currentBitmask | KINK_ROLES[role];
}

/**
 * 从位掩码中移除角色
 * @param currentBitmask 当前的位掩码
 * @param role 要移除的角色
 * @returns 新的位掩码
 */
export function removeRole(currentBitmask: number, role: KinkRole): number {
	return currentBitmask & ~KINK_ROLES[role];
}

/**
 * 切换角色状态（如果有则移除，如果没有则添加）
 * @param currentBitmask 当前的位掩码
 * @param role 要切换的角色
 * @returns 新的位掩码
 */
export function toggleRole(currentBitmask: number, role: KinkRole): boolean {
	return hasRole(currentBitmask, role);
}

/**
 * 计算两个用户的角色兼容性分数
 * @param userRoles1 用户1的角色数组
 * @param userRoles2 用户2的角色数组
 * @returns 兼容性分数 (0-1之间)
 */
export function calculateRoleCompatibility(userRoles1: KinkRole[], userRoles2: KinkRole[]): number {
	if (userRoles1.length === 0 || userRoles2.length === 0) {
		return 0;
	}

	let compatiblePairs = 0;
	let totalPairs = 0;

	for (const role1 of userRoles1) {
		for (const role2 of userRoles2) {
			totalPairs++;
			if (COMPATIBLE_ROLES[role1]?.includes(role2)) {
				compatiblePairs++;
			}
		}
	}

	return totalPairs > 0 ? compatiblePairs / totalPairs : 0;
}

/**
 * 获取与指定角色兼容的所有角色
 * @param role 指定的角色
 * @returns 兼容的角色数组
 */
export function getCompatibleRoles(role: KinkRole): KinkRole[] {
	return COMPATIBLE_ROLES[role] || [];
}

/**
 * 验证kink评分是否有效
 * @param rating 评分值
 * @returns 是否有效
 */
export function isValidKinkRating(rating: any): rating is KinkRating {
	const validRatings = ['-1', '0', '1', '2', '3', '4', '5'];
	return validRatings.includes(String(rating));
}

/**
 * 计算两个用户的kink评分匹配度
 * @param ratings1 用户1的评分对象
 * @param ratings2 用户2的评分对象
 * @returns 匹配度分数 (0-1之间)
 */
export function calculateKinkRatingCompatibility(
	ratings1: Record<string, number>,
	ratings2: Record<string, number>
): number {
	const commonCategories = Object.keys(ratings1).filter(category => 
		category in ratings2
	);

	if (commonCategories.length === 0) {
		return 0;
	}

	let totalScore = 0;
	let validComparisons = 0;

	for (const category of commonCategories) {
		const rating1 = ratings1[category];
		const rating2 = ratings2[category];

		// 如果任一用户对该类别评分为-1（绝对不要），则不兼容
		if (rating1 === -1 || rating2 === -1) {
			if (rating1 === -1 && rating2 > 0) return 0; // 完全不兼容
			if (rating2 === -1 && rating1 > 0) return 0; // 完全不兼容
			if (rating1 === -1 && rating2 === -1) continue; // 都不感兴趣，跳过
		}

		// 计算评分差异，差异越小兼容性越高
		const difference = Math.abs(rating1 - rating2);
		const maxDifference = 6; // 最大可能差异 (5 - (-1))
		const compatibility = 1 - (difference / maxDifference);
		
		totalScore += compatibility;
		validComparisons++;
	}

	return validComparisons > 0 ? totalScore / validComparisons : 0;
}

/**
 * 格式化角色列表为可读字符串
 * @param roles 角色数组
 * @param useLabels 是否使用中文标签
 * @returns 格式化的字符串
 */
export function formatRoles(roles: KinkRole[], useLabels: boolean = true): string {
	if (roles.length === 0) {
		return '未设置';
	}

	if (useLabels) {
		const { KINK_ROLE_LABELS } = require('$lib/constants/kink');
		return roles.map(role => KINK_ROLE_LABELS[role]).join(', ');
	}

	return roles.join(', ');
}

/**
 * 获取推荐的角色组合
 * @param currentRoles 当前已选择的角色
 * @returns 推荐的角色数组
 */
export function getRecommendedRoles(currentRoles: KinkRole[]): KinkRole[] {
	const recommendations: KinkRole[] = [];
	
	// 基于已选择的角色推荐兼容的角色
	for (const role of currentRoles) {
		const compatible = getCompatibleRoles(role);
		for (const compatibleRole of compatible) {
			if (!currentRoles.includes(compatibleRole) && !recommendations.includes(compatibleRole)) {
				recommendations.push(compatibleRole);
			}
		}
	}

	return recommendations.slice(0, 5); // 限制推荐数量
}
