import { redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq, ne, and, gte, lte, inArray, sql } from 'drizzle-orm';

export const load: PageServerLoad = async ({ locals }) => {
	// 确保用户已登录
	if (!locals.user) {
		throw redirect(302, '/');
	}

	const currentUser = locals.user;

	try {
		// 检查用户是否完成了基础资料
		const userProfile = await db.query.users.findFirst({
			where: eq(users.id, currentUser.id)
		});

		if (!userProfile) {
			throw redirect(302, '/');
		}

		// 检查基础资料完整性
		const hasBasicProfile = !!(
			userProfile.nickname &&
			userProfile.age &&
			userProfile.heightCm &&
			userProfile.weightKg &&
			userProfile.country &&
			userProfile.city &&
			userProfile.bodyType
		);

		if (!hasBasicProfile) {
			throw redirect(302, '/onboarding/basic');
		}

		// 检查是否有高级资料 (用于确定搜索类型)
		const hasAdvancedProfile = !!(
			userProfile.orientation &&
			userProfile.presentationStyle &&
			userProfile.relationshipStatus
		);

		// 基础搜索查询 - 只基于基础字段
		let searchQuery = db
			.select()
			.from(users)
			.where(
				and(
					ne(users.id, currentUser.id), // 排除自己
					eq(users.isActive, true), // 只显示活跃用户
					eq(users.isBanned, false) // 排除被封禁用户
				)
			)
			.limit(20)
			.orderBy(sql`${users.lastActiveAt} DESC`);

		// 如果有高级资料，可以进行更精确的匹配
		if (hasAdvancedProfile) {
			// 这里可以添加更复杂的匹配逻辑
			// 比如基于orientation, presentationStyle等的兼容性匹配
		}

		const potentialMatches = await searchQuery;

		// 过滤掉不符合基础条件的用户
		const filteredMatches = potentialMatches.filter(match => {
			// 基础过滤条件
			return (
				match.nickname &&
				match.age &&
				match.heightCm &&
				match.weightKg &&
				match.country &&
				match.city &&
				match.bodyType
			);
		});

		return {
			currentUser: {
				id: userProfile.id,
				nickname: userProfile.nickname,
				hasBasicProfile,
				hasAdvancedProfile,
				profileCompletenessScore: userProfile.profileCompletenessScore
			},
			matches: filteredMatches.map(match => ({
				id: match.id,
				nickname: match.nickname,
				age: match.age,
				heightCm: match.heightCm,
				weightKg: match.weightKg,
				country: match.country,
				city: match.city,
				bodyType: match.bodyType,
				bio: match.bio,
				profileImageUrl: match.profileImageUrl,
				hasAvatar: match.hasAvatar,
				trustScore: match.trustScore,
				lastActiveAt: match.lastActiveAt,
				// 高级字段 (如果存在)
				orientation: match.orientation,
				presentationStyle: match.presentationStyle,
				relationshipStatus: match.relationshipStatus,
				hasAdvancedProfile: !!(match.orientation && match.presentationStyle)
			})),
			searchType: hasAdvancedProfile ? 'advanced' : 'basic'
		};
	} catch (error) {
		console.error('Discover页面加载失败:', error);
		throw redirect(302, '/');
	}
};
