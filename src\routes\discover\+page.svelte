<script lang="ts">
	import { Page, Navbar, Block, BlockTitle, Button, List, ListItem } from 'konsta/svelte';
	import UserCard from './UserCard.svelte';
	import SearchFilters from './SearchFilters.svelte';
	import AdvancedProfilePrompt from './AdvancedProfilePrompt.svelte';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();

	// 状态管理
	let showFilters = $state(false);
	let currentMatchIndex = $state(0);

	// 计算当前用户
	const currentUser = $derived(data.currentUser);
	const matches = $derived(data.matches);
	const searchType = $derived(data.searchType);
	const currentMatch = $derived(matches[currentMatchIndex]);

	// Svelte 5 调试
	$inspect('Discover - currentUser:', currentUser);
	$inspect('Discover - matches count:', matches.length);
	$inspect('Discover - searchType:', searchType);

	// 处理用户操作
	function handleLike() {
		console.log('👍 喜欢用户:', currentMatch?.nickname);
		// TODO: 实现喜欢逻辑
		nextMatch();
	}

	function handlePass() {
		console.log('👎 跳过用户:', currentMatch?.nickname);
		nextMatch();
	}

	function nextMatch() {
		if (currentMatchIndex < matches.length - 1) {
			currentMatchIndex++;
		} else {
			// 没有更多匹配了
			console.log('没有更多匹配用户');
		}
	}

	function toggleFilters() {
		showFilters = !showFilters;
	}

	function handleAdvancedProfileSetup() {
		// 跳转到高级资料设置
		window.location.href = '/onboarding/advanced';
	}
</script>

<svelte:head>
	<title>发现 - BlueX</title>
</svelte:head>

<Page>
	<Navbar title="发现" />

	<!-- 搜索类型提示 -->
	<Block class="bg-blue-50 dark:bg-blue-900/20">
		<div class="flex items-center justify-between">
			<div class="flex items-center space-x-2">
				<div class="text-lg">
					{searchType === 'advanced' ? '🔍' : '🔎'}
				</div>
				<div>
					<h4 class="text-sm font-medium text-blue-900 dark:text-blue-100">
						{searchType === 'advanced' ? '高级搜索' : '基础搜索'}
					</h4>
					<p class="text-xs text-blue-700 dark:text-blue-300">
						{searchType === 'advanced' ? '基于完整资料的精准匹配' : '基于基础信息的匹配'}
					</p>
				</div>
			</div>
			<Button class="text-xs" onclick={toggleFilters}>筛选</Button>
		</div>
	</Block>

	<!-- 高级资料提示 (如果用户还没有设置) -->
	{#if !currentUser.hasAdvancedProfile}
		<AdvancedProfilePrompt onSetup={handleAdvancedProfileSetup} />
	{/if}

	<!-- 搜索筛选器 -->
	{#if showFilters}
		<SearchFilters {searchType} onClose={() => (showFilters = false)} />
	{/if}

	<!-- 匹配用户展示 -->
	{#if matches.length > 0 && currentMatch}
		<Block class="px-4">
			<UserCard user={currentMatch} {searchType} onLike={handleLike} onPass={handlePass} />
		</Block>

		<!-- 匹配进度 -->
		<Block class="text-center">
			<p class="text-sm text-gray-500">
				{currentMatchIndex + 1} / {matches.length}
			</p>
		</Block>
	{:else}
		<!-- 没有匹配用户 -->
		<Block class="py-20 text-center">
			<div class="mb-4 text-6xl">🔍</div>
			<h3 class="mb-2 text-xl font-bold">暂无匹配用户</h3>
			<p class="mb-4 text-gray-600 dark:text-gray-300">
				{searchType === 'basic' ? '尝试完善高级资料以获得更精准的匹配' : '调整搜索条件或稍后再试'}
			</p>
			{#if !currentUser.hasAdvancedProfile}
				<Button onclick={handleAdvancedProfileSetup}>设置高级资料</Button>
			{/if}
		</Block>
	{/if}

	<!-- 操作提示 -->
	<Block class="bg-gray-50 dark:bg-gray-800">
		<div class="text-center">
			<h4 class="mb-2 text-sm font-medium">操作提示</h4>
			<div class="flex justify-center space-x-8 text-xs text-gray-600 dark:text-gray-300">
				<div class="flex items-center space-x-1">
					<span>👎</span>
					<span>跳过</span>
				</div>
				<div class="flex items-center space-x-1">
					<span>👍</span>
					<span>喜欢</span>
				</div>
			</div>
		</div>
	</Block>
</Page>
