<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import {
		isAuthenticated,
		userProfile,
		registrationStatus,
		canUseBasicSearch,
		canUseAdvancedSearch,
		uiActions
	} from '$lib/stores';
	import SearchHeader from './SearchHeader.svelte';
	import SearchFilters from './SearchFilters.svelte';
	import SearchResults from './SearchResults.svelte';
	import OnboardingPrompt from './OnboardingPrompt.svelte';

	let searchMode = $state<'basic' | 'advanced' | 'super'>('basic');
	let showFilters = $state(false);

	// 响应式状态
	let user = $derived($userProfile);
	let status = $derived($registrationStatus);
	let canBasicSearch = $derived($canUseBasicSearch);
	let canAdvancedSearch = $derived($canUseAdvancedSearch);

	onMount(async () => {
		// 认证和profile加载现在在AuthGuard中处理
		// 这里只需要等待数据加载完成
		console.log('🔍 Discover页面初始化，用户状态:', {
			isAuthenticated: $isAuthenticated,
			hasProfile: !!user,
			registrationStatus: status
		});
	});

	function handleSearchModeChange(mode: 'basic' | 'advanced' | 'super') {
		// 检查用户是否有权限使用该搜索模式
		if (mode === 'advanced' && !canAdvancedSearch) {
			// 引导用户完善高级资料
			uiActions.modal.open(
				'advanced_profile_setup',
				{
					message: '要使用高级搜索，需要完善您的详细偏好设置',
					confirmText: '去设置',
					cancelText: '稍后'
				},
				undefined,
				() => {
					goto('/onboarding/advanced');
				}
			);
			return;
		}

		if (mode === 'super' && !user?.vipLevel) {
			// 引导用户升级会员
			uiActions.toast.info('超级搜索需要升级为 Pro 用户');
			return;
		}

		searchMode = mode;
	}

	function toggleFilters() {
		showFilters = !showFilters;
	}
</script>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
	<!-- 检查用户状态 -->
	{#if !$isAuthenticated}
		<div class="flex min-h-screen items-center justify-center">
			<div class="text-center">
				<div class="mb-4 text-4xl">🔐</div>
				<h2 class="mb-2 text-xl font-semibold text-gray-900 dark:text-white">请先登录</h2>
				<p class="text-gray-600 dark:text-gray-300">需要登录后才能使用搜索功能</p>
			</div>
		</div>
	{:else if status === 'new_user'}
		<!-- 新用户引导 -->
		<OnboardingPrompt />
	{:else}
		<!-- 主要搜索界面 -->
		<div class="flex h-screen flex-col">
			<!-- 搜索头部 -->
			<SearchHeader
				{searchMode}
				{canBasicSearch}
				{canAdvancedSearch}
				canSuperSearch={!!user?.vipLevel}
				onModeChange={handleSearchModeChange}
				onToggleFilters={toggleFilters}
			/>

			<!-- 搜索过滤器 -->
			{#if showFilters}
				<SearchFilters {searchMode} onClose={() => (showFilters = false)} />
			{/if}

			<!-- 搜索结果 -->
			<div class="flex-1 overflow-hidden">
				<SearchResults {searchMode} />
			</div>
		</div>
	{/if}
</div>
