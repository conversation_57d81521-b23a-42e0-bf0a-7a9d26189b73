<script lang="ts">
	import { 
		Page, 
		Navbar, 
		Block, 
		BlockTitle, 
		Button, 
		List, 
		ListItem 
	} from 'konsta/svelte';

	let testResults = $state([]);

	async function testAuthFlow() {
		// 测试1: 检查认证状态
		try {
			const statusResponse = await fetch('/api/auth/status');
			const statusData = await statusResponse.json();
			
			testResults = [...testResults, {
				test: '认证状态检查',
				status: statusResponse.ok ? '成功' : '失败',
				details: statusData,
				timestamp: new Date().toLocaleTimeString()
			}];

			// 测试2: 如果已认证，测试登出
			if (statusData.authenticated) {
				const logoutResponse = await fetch('/api/auth/logout', {
					method: 'POST'
				});
				const logoutData = await logoutResponse.json();
				
				testResults = [...testResults, {
					test: '登出测试',
					status: logoutResponse.ok ? '成功' : '失败',
					details: logoutData,
					timestamp: new Date().toLocaleTimeString()
				}];

				// 测试3: 重新登录
				const mockUser = {
					id: 123456789,
					first_name: 'Test',
					last_name: 'User',
					username: 'testuser',
					photo_url: null,
					is_premium: false
				};

				const loginResponse = await fetch('/api/auth/telegram', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						telegramUser: mockUser
					})
				});

				const loginData = await loginResponse.json();
				
				testResults = [...testResults, {
					test: '重新登录',
					status: loginResponse.ok ? '成功' : '失败',
					details: loginData,
					timestamp: new Date().toLocaleTimeString()
				}];
			}
		} catch (error) {
			testResults = [...testResults, {
				test: '认证流程测试',
				status: '错误',
				details: error.message,
				timestamp: new Date().toLocaleTimeString()
			}];
		}
	}

	function clearResults() {
		testResults = [];
	}
</script>

<svelte:head>
	<title>简单认证测试 - BlueX</title>
</svelte:head>

<Page>
	<Navbar title="简单认证测试" />

	<BlockTitle>认证流程测试</BlockTitle>
	<Block>
		<p class="text-sm text-gray-600 dark:text-gray-300 mb-4">
			这个测试将验证完整的认证流程：检查状态 → 登出 → 重新登录
		</p>
		
		<Button 
			fill 
			onclick={testAuthFlow}
		>
			开始完整认证流程测试
		</Button>
	</Block>

	{#if testResults.length > 0}
		<BlockTitle>测试结果</BlockTitle>
		<List strongIos insetIos>
			{#each testResults as result}
				<ListItem 
					title={result.test}
					after={result.timestamp}
				>
					<div slot="subtitle" class="text-sm">
						<div class="font-semibold {result.status === '成功' ? 'text-green-600' : result.status === '失败' ? 'text-red-600' : 'text-orange-600'}">
							{result.status}
						</div>
						<div class="text-gray-600 mt-1">
							{typeof result.details === 'string' ? result.details : JSON.stringify(result.details, null, 2)}
						</div>
					</div>
				</ListItem>
			{/each}
		</List>

		<Block>
			<Button 
				fill 
				color="gray" 
				onclick={clearResults}
			>
				清空结果
			</Button>
		</Block>
	{/if}
</Page>
