<script lang="ts">
	import { Block, Button } from 'konsta/svelte';

	interface Props {
		onSetup: () => void;
	}

	let { onSetup }: Props = $props();
</script>

<Block class="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
	<div class="flex items-center space-x-4">
		<div class="text-3xl">✨</div>
		<div class="flex-1">
			<h4 class="text-sm font-medium text-purple-900 dark:text-purple-100 mb-1">
				解锁高级搜索
			</h4>
			<p class="text-xs text-purple-700 dark:text-purple-300 mb-2">
				完善高级资料，获得基于性取向、展现风格、Kink偏好的精准匹配
			</p>
			<div class="flex space-x-2 text-xs text-purple-600 dark:text-purple-400">
				<span>🎯 精准匹配</span>
				<span>🔒 隐私保护</span>
				<span>⚡ SSC/RACK原则</span>
			</div>
		</div>
		<Button 
			class="text-xs"
			onclick={onSetup}
		>
			设置
		</Button>
	</div>
</Block>
