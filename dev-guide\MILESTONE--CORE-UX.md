# 里程碑 2 指南：核心用户体验 - 资料与发现

**目标:** 实现用户资料的完整填写、展示与编辑流程，并搭建基础的用户发现（搜索）功能。完成此阶段后，应用将具备第一个可玩的、有价值的核心循环。

**前置条件:** 已完成 [里程碑 1: 奠定基石 - 数据库与认证系统](./MILESTONE-1-FOUNDATION.md) 中的所有任务。

---

### 任务 2.1: 实现渐进式用户资料填写流程 (Onboarding)

这是本阶段最核心的任务，它将完美融合 Zod, Superforms, Konsta UI 和 SvelteKit Actions。

**目标:** 创建一个 `/onboarding` 页面，让新用户填写他们的“基础资料”和“高级资料”。

#### **步骤 1: 定义“契约” - 创建 Zod Schema**

在 `src/lib/schemas/profile.ts` 中，我们用 Zod 来定义用户资料的验证规则。

```typescript
// src/lib/schemas/profile.ts
import { z } from 'zod';
import { KINK_ROLES } from '$lib/constants'; // 我们稍后会创建这个文件

const basicProfileSchema = z.object({
	nickname: z.string().min(2, '昵称至少需要2个字符').max(20, '昵称不能超过20个字符'),
	age: z.number({ coerce: true }).min(18, '您必须年满18岁').max(99, '年龄不合法'),
	height_cm: z.number({ coerce: true }).min(100, '身高不合法').max(250, '身高不合法'),
	weight_kg: z.number({ coerce: true }).min(30, '体重不合法').max(200, '体重不合法'),
    country: z.string().min(1, '请选择国家'),
    city: z.string().min(1, '请填写城市'),
    body_type: z.enum(['male_body', 'female_body', 'other_body_type'], {required_error: '请选择身体类型'})
});

const advancedProfileSchema = z.object({
    orientation: z.enum(['straight', 'gay', 'lesbian', 'bisexual', 'asexual', 'demisexual', 'pansexual', 'queer', 'fluid', 'other_orientation', 'prefer_not_to_say_orientation']).optional(),
    presentation_style: z.enum(['conventional_masculine', 'rugged_masculine', 'feminine', 'androgynous_neutral', 'other_presentation_style']).optional(),
    relationship_status: z.enum(['single', 'in_a_relationship', 'complicated', 'open_relationship', 'married', 'polyamorous', 'other_relationship_status', 'prefer_not_to_say_relationship_status']).optional(),

    // 对于 bitmask 和 jsonb，我们让前端发送原始数据，在后端进行转换和验证
    kink_roles: z.array(z.nativeEnum(KINK_ROLES)).optional(), // 前端提交的是角色名称数组
    kink_ratings: z.record(z.number().min(-1).max(5)).optional() // 前端提交的是对象
});

export const profileSchema = basicProfileSchema.merge(advancedProfileSchema);

export type ProfileSchema = typeof profileSchema;

步骤 2: 编写后端逻辑 - +page.server.ts
在 src/routes/onboarding/+page.server.ts 中，我们使用 Superforms 来处理表单的加载和提交。

TypeScript

// src/routes/onboarding/+page.server.ts
import { fail, redirect } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms/server';
import { profileSchema } from '$lib/schemas/profile';
import { db } from '$lib/server/db';
import { users } from '$lib/server/db/schema';
import { eq } from 'drizzle-orm';
import type { Actions, PageServerLoad } from './$types';
import { encodeRoles } from '$lib/utils/kinkUtils'; // 我们稍后会创建这个工具函数

export const load: PageServerLoad = async ({ locals }) => {
    if (!locals.user) throw redirect(303, '/'); // 未登录用户重定向

    // 加载用户现有数据来预填充表单
    const userProfile = await db.query.users.findFirst({
        where: eq(users.id, locals.user.id)
    });

	const form = await superValidate(userProfile, profileSchema);
	return { form };
};

export const actions: Actions = {
	default: async ({ request, locals }) => {
        if (!locals.user) throw fail(401);

		const form = await superValidate(request, profileSchema);

		if (!form.valid) {
			return fail(400, { form });
		}

        // 处理特殊字段
        const bitmask = form.data.kink_roles ? encodeRoles(form.data.kink_roles) : 0;

        // 更新数据库
        await db.update(users)
            .set({
                nickname: form.data.nickname,
                age: form.data.age,
                height_cm: form.data.height_cm,
                weight_kg: form.data.weight_kg,
                country: form.data.country,
                city: form.data.city,
                body_type: form.data.body_type,
                orientation: form.data.orientation,
                presentation_style: form.data.presentation_style,
                relationship_status: form.data.relationship_status,
                kink_category_bitmask: bitmask,
                kink_ratings: form.data.kink_ratings || {},
                // 在这里可以根据字段完成度，计算并更新 profile_completeness_score
            })
            .where(eq(users.id, locals.user.id));

		throw redirect(303, '/profile'); // 成功后重定向到个人资料页
	}
};
步骤 3: 构建前端界面 - +page.svelte
使用 Konsta UI 组件来创建一个移动端原生的表单。

Svelte

<script lang="ts">
    import { superForm } from 'sveltekit-superforms/client';
    import { Page, Navbar, BlockTitle, List, ListInput, Button, Block } from 'konsta/svelte';

    export let data;
    const { form, errors, enhance } = superForm(data.form);
</script>

<Page>
    <Navbar title="完善您的资料" />

    <form method="POST" use:enhance>
        <BlockTitle>基础信息</BlockTitle>
        <List strongIos insetIos>
            <ListInput
                label="昵称"
                type="text"
                placeholder="请输入您的昵称"
                name="nickname"
                bind:value={$form.nickname}
                error={$errors.nickname}
            />
            <ListInput
                label="年龄"
                type="number"
                placeholder="请输入您的年龄"
                name="age"
                bind:value={$form.age}
                error={$errors.age}
            />
            </List>

        <BlockTitle>高级信息 (可选)</BlockTitle>
        <List strongIos insetIos>
             </List>

        <Block class="p-4">
            <Button large rounded>保存资料</Button>
        </Block>
    </form>
</Page>
任务 2.2: 实现 kink_category_bitmask 的逻辑
目标: 创建一个可复用的组件和工具函数来处理位掩码的编码和解码。

步骤 1: 创建“规则手册” - constants.ts
这是我们之前讨论过的，定义角色和其位值的映射。

TypeScript

// src/lib/constants.ts
export const KINK_ROLES = {
  TOP: 1,
  BOTTOM: 2,
  VERSATILE: 4,
  SIDE: 8,
  S_ROLE: 16,
  M_ROLE: 32,
  EDGER: 64,
  EDGEE: 128,
} as const;

export type KinkRole = keyof typeof KINK_ROLES;

// 为了方便在UI上展示，创建一个标签映射
export const KINK_ROLE_LABELS: Record<KinkRole, string> = {
    TOP: 'Top/攻',
    BOTTOM: 'Bottom/受',
    VERSATILE: 'Versatile/可攻可受',
    // ... 其他标签
};
步骤 2: 创建工具函数 - kinkUtils.ts
TypeScript

// src/lib/utils/kinkUtils.ts
import { KINK_ROLES, type KinkRole } from '$lib/constants';

export function encodeRoles(selectedRoles: KinkRole[]): number {
    return selectedRoles.reduce((bitmask, role) => bitmask | KINK_ROLES[role], 0);
}

export function decodeRoles(bitmask: number): KinkRole[] {
    const roles: KinkRole[] = [];
    // 使用 Object.keys 来遍历 KINK_ROLES
    for (const key of Object.keys(KINK_ROLES)) {
        const roleKey = key as KinkRole;
        if ((bitmask & KINK_ROLES[roleKey]) > 0) {
            roles.push(roleKey);
        }
    }
    return roles;
}
步骤 3: 创建UI组件 - KinkRoleSelector.svelte
在您的表单中，您可以使用这个可复用的组件来让用户选择他们的角色，它会处理好与 superForm 的数据绑定。

任务 2.3: 构建“查看/编辑个人资料”页面 (/profile)
目标: 创建一个页面，清晰地展示用户的所有资料，并提供编辑入口。

后端 +page.server.ts: load 函数从数据库中查询当前登录用户的完整信息并返回。
前端 +page.svelte:
使用 Konsta UI 的 List 和 ListItem 组件，以只读的方式展示 data.userProfile 中的所有字段。
对于 kink_category_bitmask 和 kink_ratings，使用我们创建的 decodeRoles 等工具函数将其转换为人类可读的格式。
在页面顶部或底部放置一个“编辑资料”按钮，链接到 /onboarding 页面。
任务 2.4: 构建基础“发现”页面 (/discover)
目标: 实现应用的核心功能——让用户能看到其他人。

后端 +page.server.ts:
load 函数接收来自 URL 的查询参数（例如 ?age_min=20&country=Japan）。
使用 Drizzle ORM，根据这些参数构建一个动态的 WHERE 子句来查询 users 表。
此阶段只使用“基础数据字段”进行过滤。
返回一个用户列表 users。
前端 +page.svelte:
在页面顶部创建一个搜索表单，包含对“基础数据字段”的筛选控件。
使用 {#each data.users as user} 循环来渲染用户卡片列表。
提交搜索表单时，SvelteKit 会自动将表单数据附加到 URL 上并重新运行 load 函数，实现页面数据的刷新。
AI Copilot 指示: 里程碑 2 的任务已详细拆解。请从任务 2.1 开始，逐步实现。每完成一个关键步骤，记得回来更新 PROGRESS_LOG.md。
```
