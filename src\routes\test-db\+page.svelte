<script lang="ts">
	import { 
		Page, 
		Navbar, 
		Block, 
		BlockTitle 
	} from 'konsta/svelte';
	import type { PageData } from './$types';

	let { data }: { data: PageData } = $props();
</script>

<svelte:head>
	<title>数据库测试 - BlueX</title>
</svelte:head>

<Page>
	<Navbar title="数据库测试" />

	<BlockTitle>数据库连接状态</BlockTitle>
	<Block>
		<div class="text-center">
			<div class="text-6xl mb-4">
				{data.success ? '✅' : '❌'}
			</div>
			<h2 class="text-xl font-bold mb-2">
				{data.success ? '连接成功' : '连接失败'}
			</h2>
			<p class="text-gray-600 dark:text-gray-300">
				{data.message}
			</p>
			{#if data.success}
				<p class="text-sm text-gray-500 mt-2">
					用户数量: {data.userCount}
				</p>
			{:else if data.error}
				<p class="text-sm text-red-500 mt-2">
					错误: {data.error}
				</p>
			{/if}
		</div>
	</Block>
</Page>
